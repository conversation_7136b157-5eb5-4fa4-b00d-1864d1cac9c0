Stack trace:
Frame         Function      Args
0007FFFFBBA0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFBBA0, 0007FFFFAAA0) msys-2.0.dll+0x2118E
0007FFFFBBA0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFBE78) msys-2.0.dll+0x69BA
0007FFFFBBA0  0002100469F2 (00021028DF99, 0007FFFFBA58, 0007FFFFBBA0, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBBA0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBBA0  00021006A545 (0007FFFFBBB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFBE80  00021006B9A5 (0007FFFFBBB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8C9B10000 ntdll.dll
7FF8C8930000 KERNEL32.DLL
7FF8C75B0000 KERNELBASE.dll
7FF8C8A30000 USER32.dll
7FF8C74A0000 win32u.dll
7FF8C9010000 GDI32.dll
000210040000 msys-2.0.dll
7FF8C7A10000 gdi32full.dll
7FF8C71A0000 msvcp_win.dll
7FF8C7320000 ucrtbase.dll
7FF8C7B50000 advapi32.dll
7FF8C9170000 msvcrt.dll
7FF8C7D60000 sechost.dll
7FF8C9040000 RPCRT4.dll
7FF8C7580000 bcrypt.dll
7FF8C6A50000 CRYPTBASE.DLL
7FF8C7290000 bcryptPrimitives.dll
7FF8C8A00000 IMM32.DLL
