{"name": "skeye", "version": "1.3.1", "scripts": {"prepare": "husky install", "ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "updateVersion": "npx ts-node -O '{\"module\": \"commonjs\"}' git.version.ts ", "semantic-release": "standard-version"}, "private": true, "dependencies": {"@angular-magic/ngx-gp-autocomplete": "^2.0.2", "@angular/animations": "^16.2.12", "@angular/cdk": "16", "@angular/common": "^16.2.12", "@angular/compiler": "^16.2.12", "@angular/core": "^16.2.12", "@angular/fire": "7.6.0", "@angular/forms": "^16.2.12", "@angular/google-maps": "^19.1.3", "@angular/material-moment-adapter": "^16.2.12", "@angular/platform-browser": "^16.2.12", "@angular/platform-browser-dynamic": "^16.2.12", "@angular/router": "^16.2.12", "@babel/runtime": "^7.26.7", "@fortawesome/angular-fontawesome": "0.10.2", "@fortawesome/fontawesome-svg-core": "^6.1.1", "@fortawesome/free-brands-svg-icons": "^6.1.1", "@fortawesome/free-regular-svg-icons": "^6.1.1", "@fortawesome/free-solid-svg-icons": "^6.1.1", "@fullcalendar/angular": "^6.1.0", "@fullcalendar/core": "^6.1.0", "@fullcalendar/daygrid": "^6.1.0", "@fullcalendar/interaction": "^6.1.1", "@fullcalendar/timegrid": "^6.1.1", "@googlemaps/js-api-loader": "^1.16.8", "@mtnair/ngx-pinch-zoom": "^2.5.12", "@popperjs/core": "^2.11.8", "@sentry/angular": "^8.51.0", "@sentry/tracing": "^7.120.3", "@types/google.maps": "^3.58.1", "angular-mentions": "^1.5.0", "bootstrap": "^5.1.3", "chart.js": "^3.9.1", "file-saver": "^2.0.5", "firebase": "^9.22.0", "libphonenumber-js": "^1.7.51", "lodash": "^4.17.15", "moment": "^2.30.1", "ng2-search-filter": "^0.5.1", "ngx-bootstrap": "^19.0.2", "ngx-google-places-autocomplete": "^2.0.5", "ngx-image-zoom": "^3.0.0", "ngx-infinite-scroll": "^19.0.0", "ngx-mask": "^13.1.1", "ngx-ui-switch": "^13.0.2", "popper.js": "^1.16.1", "primeicons": "6", "primeng": "16", "quill": "^1.3.7", "rxjs": "^7.8.1", "tslib": "^2.3.0", "webpack": "^5.97.1", "xlsx": "^0.18.5", "zone.js": "~0.13.3"}, "devDependencies": {"@angular-devkit/build-angular": "16.2.16", "@angular-eslint/builder": "16", "@angular-eslint/eslint-plugin": "16", "@angular-eslint/eslint-plugin-template": "16", "@angular-eslint/schematics": "16", "@angular-eslint/template-parser": "16", "@angular/cli": "16.2.16", "@angular/compiler-cli": "^16.2.12", "@semantic-release/git": "^10.0.1", "@semantic-release/gitlab": "^8.1.0", "@semantic-release/npm": "^9.0.1", "@types/file-saver": "^2.0.5", "@types/googlemaps": "^3.43.3", "@types/jasmine": "~3.8.0", "@types/node": "18", "@typescript-eslint/eslint-plugin": "4.28.2", "@typescript-eslint/parser": "4.28.2", "eslint": "^7.26.0", "git-branch-is": "^4.0.0", "husky": "^7.0.4", "jasmine-core": "~3.8.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "prettier": "^2.5.1", "pretty-quick": "^3.1.3", "semantic-release": "^19.0.2", "standard-version": "^9.3.2", "typescript": "4.9.5"}}