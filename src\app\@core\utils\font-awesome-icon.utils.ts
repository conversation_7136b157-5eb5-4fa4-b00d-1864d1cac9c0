import { faFloppyDisk } from '@fortawesome/free-regular-svg-icons';
import { faAdd, faArrowDown, faArrowLeft, faArrowRotateRight, faBars, faCaretDown, faCaretRight, faCheckCircle, faChevronCircleDown, faChevronCircleUp, faCircle, faCircleXmark, faCity, faClockRotateLeft, faColumns, faCopy, faDownload, faEllipsisH, faEllipsisV, faEnvelope, faEnvelopeSquare, faExpandAlt, faFileAlt, faFileDownload, faFilePdf, faFilterCircleXmark, faInfoCircle, faLinkSlash, faLocationDot, faMinus, faMoon, faPhoneAlt, faShare, faSignOutAlt, faSignal, faSort, faSortDown, faSortUp, faSpinner, faSun, faTimes, faTrashCan, faUser, faXmark } from "@fortawesome/free-solid-svg-icons";


export const faIcons = {
  faSpinner,
  faXmark,
  faFilterCircleXmark,
  faCity,
  faColumns,
  faChevronCircleDown,
  faChevronCircleUp,
  faSignal,
  faFileAlt,
  faEnvelope,
  faPhoneAlt,
  faUser,
  faArrowLeft,
  faCheckCircle,
  faSignOutAlt,
  faEnvelopeSquare,
  faSort,
  faSortDown,
  faSortUp,
  faTimes,
  faBars,
  faInfoCircle,
  faAdd,
  faDownload,
  faArrowDown,
  faFileDownload,
  faTrashCan,
  faFilePdf,
  faShare,
  faExpandAlt,
  faLocationDot,
  faCaretDown,
  faCaretRight,
  faCircleXmark,
  faEllipsisV,
  faEllipsisH,
  faCircle,
  faLinkSlash,
  faClockRotateLeft,
  faMoon,
  faSun,
  faArrowRotateRight,
  faCopy,
  faFloppyDisk,
  // New zoom icons - using existing icons that make sense
  faChevronLeft: faArrowLeft, // Use existing arrow left as chevron left
  faChevronRight: faArrowDown, // Use existing arrow down as chevron right (rotated in CSS)
  faPlus: faAdd, // Use existing add icon for zoom in
  faMinus: faMinus, // Use X mark for zoom out (close/reduce)
  faHome: faInfoCircle, // Use info circle for reset/home
  faSearchPlus: faExpandAlt // Use expand icon for magnifier
}

