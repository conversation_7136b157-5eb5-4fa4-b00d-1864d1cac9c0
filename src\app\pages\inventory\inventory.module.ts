import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UiSwitchConfig } from '@constants/*';
import { VendorsModule } from '@pages/administration/pages/vendors/vendors.module';
import { ColumnDropdownModule } from '@pages/common-table-column/column-dropdown.module';
import { CrmCustomerInventoryWrapperModule } from '@pages/crm/pages/crm-customer/crm-customer-inventory-wrapper/crm-customer-inventory-wrapper.module';
import { SoldTruckBoardModule } from '@pages/pipeline/pages/sold-truck-board/sold-truck-board.module';
import { StockTruckBoardModule } from '@pages/pipeline/pages/stock-truck-board/stock-truck-board.module';
import { PublicInventoriesModule } from '@pages/public-inventories/public-inventories.module';
import { IncomingTruckModule } from '@pages/transport/pages/incoming-truck/incoming-truck.module';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { TabsModule } from 'ngx-bootstrap/tabs';

import { UiSwitchModule } from 'ngx-ui-switch';
import { AccordionModule } from 'primeng/accordion';
import { ConfirmationService } from 'primeng/api';
import { AvatarModule } from 'primeng/avatar';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { CarouselModule } from 'primeng/carousel';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { ContextMenuModule } from 'primeng/contextmenu';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { DragDropModule } from 'primeng/dragdrop';
import { DropdownModule } from 'primeng/dropdown';
import { EditorModule } from 'primeng/editor';
import { ListboxModule } from 'primeng/listbox';
import { MenuModule } from 'primeng/menu';
import { MessageModule } from 'primeng/message';
import { MessagesModule } from 'primeng/messages';
import { MultiSelectModule } from 'primeng/multiselect';
import { OrderListModule } from 'primeng/orderlist';
import { PanelModule } from 'primeng/panel';
import { ProgressBarModule } from 'primeng/progressbar';
import { RadioButtonModule } from 'primeng/radiobutton';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { TagModule } from 'primeng/tag';
import { TimelineModule } from 'primeng/timeline';
import { TooltipModule } from 'primeng/tooltip';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { InventoryRoutingModule } from './inventory-routing.module';
import { InventoryComponent } from './inventory.component';
import { DuplicateInventoryComponent } from './pages/duplicate-inventory/duplicate-inventory.component';
import { InventoryAddWrapperModule } from './pages/inventory-add-wrapper/inventory-add-wrapper.module';
import { InventoryGeneralInfoModule } from './pages/inventory-add/inventory-general-tab/inventory-general-info/inventory-general-info.module';
import { InventoryHoldModule } from './pages/inventory-hold/inventory-hold.module';
import { InventoryListComponent } from './pages/inventory-list/inventory-list.component';
import { InventorySoldModule } from './pages/inventory-sold/inventory-sold.module';
import { PreferenceListComponent } from './pages/preference-list/preference-list.component';
import { SavePreferenceComponent } from './pages/save-preference/save-preference.component';
import { ViewInventoryDetailsComponent } from './pages/view-inventory-details/view-inventory-details.component';

@NgModule({
  declarations: [
    InventoryListComponent,
    InventoryComponent,
    DuplicateInventoryComponent,
    ViewInventoryDetailsComponent,
    SavePreferenceComponent,
    PreferenceListComponent,
  ],
  imports: [
    CommonModule,
    ConfirmDialogModule,
    InventoryRoutingModule,
    SharedComponentsModule,
    TabsModule.forRoot(),
    DirectivesModule,
    FontAwesomeIconsModule,
    TableModule,
    DropdownModule,
    ScrollingModule,
    MultiSelectModule,
    FormsModule,
    UiSwitchModule.forRoot(UiSwitchConfig),
    SidebarModule,
    ReactiveFormsModule,
    ConfirmPopupModule,
    CheckboxModule,
    RadioButtonModule,
    TabViewModule,
    AccordionModule,
    CalendarModule,
    MessageModule,
    VendorsModule,
    EditorModule,
    MessagesModule,
    MessageModule,
    PanelModule,
    ProgressBarModule,
    CardModule,
    TimelineModule,
    TooltipModule,
    DividerModule,
    SoldTruckBoardModule,
    CarouselModule,
    DialogModule,
    StockTruckBoardModule,
    AvatarModule,
    InventoryGeneralInfoModule,
    InventoryAddWrapperModule,
    IncomingTruckModule,
    OrderListModule,
    CheckboxModule,
    ListboxModule,
    ColumnDropdownModule,
    DragDropModule,
    MenuModule,
    ContextMenuModule,
    InventorySoldModule,
    InventoryHoldModule,
    PublicInventoriesModule,
    CrmCustomerInventoryWrapperModule,
    TagModule
  ],
  providers: [ConfirmationService],
  exports: []

})
export class InventoryModule { }
