<form class="inventory-photos">
  <section>
    <p-accordion class="nested-accordion" [multiple]="true">
      <div class="row example-container" cdkDropListGroup>
        <div class="col-md-6 col-12">
          <p-panel>
            <ng-template pTemplate="header">
              <div class="accordion-header" [ngClass]="{ active: accordionTabs.publicPhotos }">
                <div class="public-photos">
                  <span class="header-span">Public Photos</span>
                  <div class="rectangle-green">
                    {{ selectedPublicPhotosCount }}
                  </div>
                </div>
                <div class="photos-switch d-flex align-items-center">
                  <label class="switch">
                    <input
                      type="checkbox"
                      [(ngModel)]="isPublicPhotosSelectAll"
                      name="selectAllPublicPhotos"
                      (change)="onToggleChange(inventoryPhotoType.PUBLIC_PHOTO)"
                      [ngModelOptions]="{ standalone: true }"
                    />
                    <span class="slider round"></span>
                  </label>
                  <span class="toggle-label show-label">{{ selectDelectLabelPublic }}</span>
                  <button
                    class="btn btn-p btn-primary"
                    type="button"
                    (click)="downloadSelectedImages(inventoryPhotoType.PUBLIC_PHOTO)"
                    [disabled]="!hasSelectedPublicPhotos()"
                  >
                    <em class="pi pi-download" style="font-size: 20px; padding-right: 8px"></em>
                    <span class="show-label">Download Photos</span>
                  </button>
                  <button
                    *ngIf="!isViewMode"
                    class="btn btn-p btn-primary ml-5"
                    type="button"
                    [disabled]="!hasSelectedPublicPhotos()"
                    (click)="deleteAllSelectedImagesConfirmation(inventoryPhotoType.PUBLIC_PHOTO, $event)"
                  >
                    Delete Photos
                  </button>
                </div>
              </div>
            </ng-template>
            <ng-template pTemplate="content">
              <div class="inventory-images-public">
                <div class="file-wrapper" [ngClass]="{ 'mb-4': fileUploadProgressesPublic.length }">
                  <ng-container [ngTemplateOutlet]="attachmentTemplatePublic" *ngIf="!isViewMode"></ng-container>
                  <div
                    class="photos-grid"
                    cdkDropList
                    [cdkDropListData]="publicPhotos"
                    (cdkDropListDropped)="drop($event, inventoryPhotoType.PUBLIC_PHOTO)"
                    cdkDropListOrientation="horizontal"
                  >
                    <div
                      *ngFor="let imgInfo of publicPhotos; let index = index; trackBy: trackByImageId"
                      cdkDrag
                      class="photo-item"
                      [cdkDragData]="imgInfo"
                      (cdkDragStarted)="onDragStarted()"
                      (cdkDragReleased)="onDragReleased()"
                    >
                      <div class="photo-placeholder" *cdkDragPlaceholder></div>
                      <ng-container
                        [ngTemplateOutlet]="imageTemplate"
                        [ngTemplateOutletContext]="{
                          imgInfo: imgInfo,
                          selectedPhotoindex: index,
                          callBackFn: onSelectPublicPhoto.bind(this),
                          isSelectedFn: isPublicPhotoSelected.bind(this)
                        }"
                      >
                      </ng-container>
                    </div>
                    <ng-container [ngTemplateOutlet]="addImageTemplatePublic" *ngIf="isPublicFileUploadProgress"></ng-container>
                  </div>
                </div>
              </div>
            </ng-template>
          </p-panel>
        </div>
        <div class="col-md-6 col-12">
          <p-panel>
            <ng-template pTemplate="header">
              <div class="accordion-header" [ngClass]="{ active: accordionTabs.internalPhotos }">
                <div class="internal-photos">
                  <span class="header-span">Internal Photos</span>
                  <div class="rectangle-gray">
                    {{ selectedInternalPhotosCount }}
                  </div>
                </div>
                <div class="photos-switch d-flex align-items-center">
                  <label class="switch">
                    <input
                      type="checkbox"
                      [(ngModel)]="isInternalPhotosSelectAll"
                      (change)="onToggleChange(inventoryPhotoType.INTERNAL_PHOTO)"
                      [ngModelOptions]="{ standalone: true }"
                    />
                    <span class="slider round"></span>
                  </label>
                  <span class="toggle-label show-label">{{ selectDelectLabelInternal }}</span>
                  <button
                    [disabled]="!hasSelectedInternalPhotos()"
                    class="btn btn-p btn-primary"
                    type="button"
                    (click)="downloadSelectedImages(inventoryPhotoType.INTERNAL_PHOTO)"
                  >
                    <em class="pi pi-download" style="font-size: 20px; padding-right: 8px"> </em>
                    <span class="show-label">Download Photos</span>
                  </button>
                  <button
                    class="btn btn-p btn-primary ml-5"
                    type="button"
                    *ngIf="!isViewMode"
                    [disabled]="!hasSelectedInternalPhotos()"
                    (click)="deleteAllSelectedImagesConfirmation(inventoryPhotoType.INTERNAL_PHOTO, $event)"
                  >
                    Delete Photos
                  </button>
                </div>
              </div>
            </ng-template>
            <ng-template pTemplate="content">
              <div class="inventory-images-internal">
                <div class="file-wrapper">
                  <ng-container [ngTemplateOutlet]="attachmentTemplateInternal" *ngIf="!isViewMode"></ng-container>
                  <div
                    class="photos-grid"
                    cdkDropList
                    [cdkDropListData]="internalPhotos"
                    (cdkDropListDropped)="drop($event, inventoryPhotoType.INTERNAL_PHOTO)"
                    cdkDropListOrientation="horizontal"
                  >
                    <div
                      *ngFor="let imgInfo of internalPhotos; let index = index; trackBy: trackByImageId"
                      cdkDrag
                      class="photo-item"
                      [cdkDragData]="imgInfo"
                      (cdkDragStarted)="onDragStarted()"
                      (cdkDragReleased)="onDragReleased()"
                    >
                      <div class="photo-placeholder" *cdkDragPlaceholder></div>
                      <ng-container
                        [ngTemplateOutlet]="imageTemplate"
                        [ngTemplateOutletContext]="{
                          imgInfo: imgInfo,
                          selectedPhotoindex: index,
                          callBackFn: onSelectInternalPhoto.bind(this),
                          isSelectedFn: isInternalPhotoSelected.bind(this)
                        }"
                      >
                      </ng-container>
                    </div>
                    <ng-container [ngTemplateOutlet]="addImageTemplateInternal" *ngIf="!isPublicFileUploadProgress"></ng-container>
                  </div>
                </div>
              </div>
            </ng-template>
          </p-panel>
        </div>
      </div>
    </p-accordion>
  </section>
</form>

<ng-template #publicPhotosTemplate></ng-template>
<ng-template #internalPhotosTemplate></ng-template>

<ng-template #imageDragAndDropTemplate></ng-template>

<ng-template
  #imageTemplate
  let-imgInfo="imgInfo"
  let-selectedPhotoindex="selectedPhotoindex"
  let-callBackFn="callBackFn"
  let-isSelectedFn="isSelectedFn"
>
  <div class="image-main-wrapper">
    <div class="files" *ngIf="!imgInfo?.fileUrl" (mouseenter)="focusedImage = imgInfo" (mouseleave)="focusedImage = null">
      <div
        class="img-wrapper"
        [ngClass]="{
          highlight: imgInfo.displayPicture,
          highlights: isSelectedFn(imgInfo) && imgInfo.displayPicture && imgInfo?.isSelected,
          active: !imgInfo?.displayPicture && imgInfo.isSelected
        }"
      >
        <div class="file-box-wrapper">
          <div class="file-box" [style.background-image]="'url(' + imgInfo.binary + ')'">
            <div class="photo-actions" *ngIf="!isViewMode">
              <ng-container *ngIf="focusedImage?.tempId === imgInfo?.tempId">
                <button class="btn btn-primary photo-detail" (click)="onViewPhotoDetail(imgInfo)">Photo Details</button>
              </ng-container>
            </div>
            <div class="delete-icon uploaded-image show-label" *ngIf="!isViewMode">
              <ng-container *ngIf="focusedImage?.tempId === imgInfo?.tempId">
                <em class="pi pi-download" (click)="onDownloadUploadedImage(imgInfo.binary)"></em>
                <em class="pi pi-trash" (click)="fileUploadDelete(imgInfo, imgInfo.photoType === inventoryPhotoType.PUBLIC_PHOTO)"></em>
                <em class="pi pi-eye" (click)="onViewImage(imgInfo.binary)"></em>
              </ng-container>
            </div>
          </div>
        </div>
        <div
          class="img-checkbox-display-picture"
          *ngIf="imgInfo.photoType === inventoryPhotoType.PUBLIC_PHOTO && !isViewMode && focusedImage?.tempId === imgInfo?.tempId"
        >
          <p-checkbox
            [(ngModel)]="imgInfo.displayPicture"
            [binary]="true"
            (onChange)="displayPictureToggle(imgInfo)"
            [readonly]="imgInfo.displayPicture"
          >
          </p-checkbox>
          <label class="preview-picture">Thumbnail</label>
        </div>
        <div class="img-checkbox">
          <p-checkbox [(ngModel)]="imgInfo.isSelected" name="addPhoto" [binary]="true" (onChange)="onSelectNewUploadedPhoto()">
          </p-checkbox>
        </div>
      </div>
    </div>

    <div
      class="img-wrapper"
      (mouseenter)="focusedImage = imgInfo"
      *ngIf="imgInfo?.fileUrl"
      [ngClass]="{
        highlight: imgInfo.displayPicture,
        highlights: isSelectedFn(imgInfo) && imgInfo.displayPicture && imgInfo?.isSelected,
        active: !imgInfo?.displayPicture && imgInfo.isSelected
      }"
      (mouseleave)="focusedImage = null"
      (click)="callBackFn(imgInfo)"
    >
      <img [src]="imgInfo?.fileUrl" [alt]="" class="img-fluid" />
      <ng-container *ngIf="focusedImage?.id === imgInfo.id">
        <div class="photo-actions" *ngIf="!isViewMode">
          <button class="btn btn-primary photo-detail" (click)="onViewPhotoDetail(imgInfo)">Photo Details</button>
        </div>
        <div class="delete-icon show-label" *ngIf="!isViewMode">
          <em class="pi pi-download" (click)="onDownloadServerImage(imgInfo)"></em>
          <em class="pi pi-trash" (click)="onDelete(imgInfo.id, $event, imgInfo.photoType)"></em>
          <em class="pi pi-eye" (click)="onViewImage(imgInfo?.fileUrl)"></em>
        </div>
        <div class="img-checkbox-display-picture" *ngIf="imgInfo.photoType === inventoryPhotoType.PUBLIC_PHOTO && !isViewMode">
          <p-checkbox
            [(ngModel)]="imgInfo.displayPicture"
            [binary]="true"
            (onChange)="displayPictureToggle(imgInfo)"
            [readonly]="imgInfo.displayPicture"
          >
          </p-checkbox>
          <label class="preview-picture">Thumbnail</label>
        </div>
      </ng-container>

      <div class="img-checkbox">
        <p-checkbox [(ngModel)]="imgInfo.isSelected" name="x" [binary]="true" (onChange)="onSelectSinglePhoto(imgInfo)"> </p-checkbox>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #noPhotos>
  <div class="w-100 text-center">
    <p class="mb-0">No photos</p>
  </div>
</ng-template>

<ng-template #attachmentTemplatePublic>
  <div class="drop-zone img-wrapper w-100">
    <span class="drop-zone__prompt w-100">
      <em class="pi pi-upload"></em>
      <p class="title">Drop images to attach</p>
      <p class="subtitle">or click to browse</p>
    </span>
    <input
      type="file"
      name="myFile"
      class="drop-zone__input w-100"
      #inputElement
      [accept]="constants.allowedImgFormats"
      (change)="onFileSelect($event, true)"
      multiple
    />
  </div>
</ng-template>

<ng-template #attachmentTemplateInternal>
  <div class="drop-zone img-wrapper w-100">
    <span class="drop-zone__prompt w-100">
      <em class="pi pi-upload"></em>
      <p class="title">Drop images to attach</p>
      <p class="subtitle">or click to browse</p>
    </span>
    <input
      type="file"
      name="myFile1"
      class="drop-zone__input w-100"
      #inputElement
      [accept]="constants.allowedImgFormats"
      (change)="onFileSelect($event, false)"
      multiple
    />
  </div>
</ng-template>

<ng-template #addImageTemplatePublic>
  <div *ngFor="let fileProgress of fileUploadProgressesPublic; let fileIndex = index; trackBy: trackByFile" class="files">
    <div class="img-wrapper" (mouseenter)="focusUploadedImage = fileProgress" (mouseleave)="focusedImage = null">
      <div class="file-box-wrapper">
        <div
          class="file-box"
          [ngClass]="{ 'in-progress': !fileProgress?.isResolved }"
          [style.background-image]="'url(' + fileProgress.fileProperty?.fileUrl + ')'"
        >
          <div class="delete-icon uploaded-image show-label" *ngIf="!isViewMode">
            <em class="pi pi-download" (click)="onDownloadUploadedImage(fileProgress.fileProperty?.fileUrl)"></em>
            <em class="pi pi-trash" (click)="fileUploadDelete(fileIndex, true)"></em>
            <em class="pi pi-eye" (click)="onViewImage(fileProgress.fileProperty?.fileUrl)"></em>
          </div>
        </div>
        <div class="file-progress" *ngIf="!fileProgress.isResolved && isPublicFileUploadProgress">
          <p-progressBar *ngIf="isPublicFileUploadProgress" [value]="fileProgress?.progress$ | async" [showValue]="true"></p-progressBar>
        </div>
      </div>
      <div class="img-checkbox">
        <p-checkbox [(ngModel)]="fileProgress.isSelected" name="addPhoto" [binary]="true" (onChange)="onSelectNewUploadedPhoto()">
        </p-checkbox>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #addImageTemplateInternal>
  <div *ngFor="let fileProgress of fileUploadProgressesInternal; let fileIndex = index; trackBy: trackByFile" class="files">
    <div class="img-wrapper" (mouseenter)="focusUploadedImage = fileProgress" (mouseleave)="focusedImage = null">
      <div class="file-box-wrapper">
        <div
          class="file-box"
          [ngClass]="{ 'in-progress': !fileProgress?.isResolved }"
          [style.background-image]="'url(' + fileProgress.fileProperty?.fileUrl + ')'"
        >
          <div class="delete-icon uploaded-image show-label" *ngIf="!isViewMode">
            <em class="pi pi-download" (click)="onDownloadUploadedImage(fileProgress.fileProperty?.fileUrl)"></em>
            <em class="pi pi-trash" (click)="fileUploadDelete(fileIndex, false)"></em>
            <em class="pi pi-eye" (click)="onViewImage(fileProgress.fileProperty?.fileUrl)"></em>
          </div>
        </div>
        <div class="file-progress" *ngIf="!isPublicFileUploadProgress && !fileProgress.isResolved">
          <p-progressBar *ngIf="!isPublicFileUploadProgress" [value]="fileProgress?.progress$ | async" [showValue]="true"></p-progressBar>
        </div>
      </div>
      <div class="img-checkbox">
        <p-checkbox [(ngModel)]="fileProgress.isSelected" name="fileProgress" [binary]="true" (onChange)="onSelectNewUploadedPhoto()">
        </p-checkbox>
      </div>
    </div>
    <div></div>
  </div>
</ng-template>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="showPhotoDetails"
  position="right"
  (onHide)="showPhotoDetails = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  [fullScreen]="true"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-inventory-photo-details
    (onClose)="onAddEditPopupClose($event)"
    *ngIf="showPhotoDetails"
    (showNextImage)="showNextImage($event)"
    (showPreviousImage)="showPreviousImage($event)"
    (changePreviewImage)="displayPictureToggle($event)"
    (updateDetailsOfNewlyUploadedPhoto)="updateDetailsOfNewlyUploadedPhoto($event)"
    [imageDetails]="imageDetails"
    [publicPhotos]="publicPhotos"
    [internalPhotos]="internalPhotos"
    [isDisplayPictureAssigned]="isDisplayPictureSet"
  >
  </app-inventory-photo-details>
</p-sidebar>

<p-dialog
  [(visible)]="showEnlargedImage"
  [modal]="true"
  [dismissableMask]="true"
  [showHeader]="false"
  [closable]="true"
  styleClass="transparent-background"
>
  <fa-icon
    [icon]="faIcons.faTimes"
    class="close-icon photos-close-icon"
    (click)="showEnlargedImage = false && (viewImageScr = '')"
  ></fa-icon>
  <img class="view-zoomed-image" [src]="viewImageScr" alt="" />
</p-dialog>
