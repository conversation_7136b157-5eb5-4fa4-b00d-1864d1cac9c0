@import 'src/assets/scss/variables';

.inventory-images-public {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: inherit;

  .img-wrapper,
  .img-wrapper img {
    height: 150px;
    width: 200px;
    border-radius: 3px;
    position: relative;

    &.highlight {
      border: 3px solid var(--success-color) !important;
      min-height: 156px;
    }

    &.highlights {
      border: 3px solid var(--success-color) !important;
    }

    &:hover {
      cursor: pointer;
      border: 1px solid var(--active-color);
    }

    &.highlights {
      border: 3px solid var(--success-color) !important;
    }

    &.active {
      border: 2px solid var(--active-color) !important;
    }
  }

  .img-wrapper {
    min-height: 119px;
  }

  .file-wrapper {
    width: 100%;
  }

  .cdk-drop-list {
    min-height: 180px;
    flex-wrap: wrap;
  }

  .photos-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    padding: 15px;
  }

  .photo-item {
    width: 200px;
    height: 150px;
    position: relative;
    border-radius: 8px;
    overflow: hidden;

    @media only screen and (max-width: 768px) {
      width: 150px;
      height: 120px;
    }

    @media only screen and (max-width: 500px) {
      width: 120px;
      height: 100px;
    }
  }

  // Native drag and drop styles
  .draggable-item {
    cursor: grab;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      transform: scale(1.02);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &:active {
      cursor: grabbing;
    }

    &.drag-over {
      border: 2px dashed #007bff;
      background: rgba(0, 123, 255, 0.1);
      transform: scale(1.05);
      box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
    }

    // Add a visual indicator for draggable items
    &::before {
      content: '⋮⋮';
      position: absolute;
      top: 5px;
      right: 5px;
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
      font-weight: bold;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
      z-index: 10;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover::before {
      opacity: 1;
    }
  }




  .photo-detail {
    font-size: 13px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 20px;
    height: 30px;
    padding-left: 5px;
    padding-right: 5px;
  }

  .photo-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
  }

  .delete-icon {
    position: absolute;
    top: 5px;
    right: 70px;
    padding: 4px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;

    .pi {
      font-size: 12px;
      color: red;
      margin: 3px;
      background-color: var(--white-color);
      border-radius: 50%;
      padding: 5px;
    }

    &:hover {
      .pi {
        font-size: 13px;
      }
    }
  }

  .img-checkbox {
    top: 5px;
    left: 5px;
    position: absolute;

    ::ng-deep .p-checkbox .p-checkbox-box {
      border-radius: 50%;
      width: 17px;
      height: 17px;
    }
  }
}

.inventory-images-internal {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: inherit;

  .img-wrapper,
  .img-wrapper img {
    height: 150px;
    width: 200px;
    border-radius: 3px;
    position: relative;

    &:hover {
      cursor: pointer;
      border: 1px solid var(--active-color);
    }

    &.active {
      border: 2px solid var(--active-color) !important;
    }
  }

  .img-wrapper {
    min-height: 119px;
  }

  .img-wrapper:not(.drop-zone) {
    border: 1px solid transparent;
  }

  .photo-detail {
    font-size: 13px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 20px;
    height: 30px;
    padding-left: 5px;
    padding-right: 5px;
  }

  .file-wrapper {
    width: 100%;
  }

  .cdk-drop-list {
    min-height: 180px;
    flex-wrap: wrap;
  }

  // Internal photos use the same styling as public photos
  .photos-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    padding: 15px;
  }

  .photo-item {
    width: 200px;
    height: 150px;
    position: relative;
    border-radius: 8px;
    overflow: hidden;

    @media only screen and (max-width: 768px) {
      width: 150px;
      height: 120px;
    }

    @media only screen and (max-width: 500px) {
      width: 120px;
      height: 100px;
    }
  }

  .photo-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
  }

  .delete-icon {
    position: absolute;
    top: 5px;
    right: 70px;
    padding: 4px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;

    .pi {
      font-size: 12px;
      color: red;
      margin: 3px;
      background-color: var(--white-color);
      border-radius: 50%;
      padding: 5px;
    }

    &:hover {
      .pi {
        font-size: 13px;
      }
    }
  }

  .img-checkbox {
    top: 5px;
    left: 5px;
    position: absolute;

    ::ng-deep .p-checkbox .p-checkbox-box {
      border-radius: 50%;
      width: 17px;
      height: 17px;
    }
  }
}

// Internal photos section - same layout as public photos
.inventory-images-internal {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: inherit;

  .img-wrapper,
  .img-wrapper img {
    height: 150px;
    width: 200px;
    border-radius: 3px;
    position: relative;

    &.highlight {
      border: 3px solid var(--success-color) !important;
      min-height: 156px;
    }

    &.highlights {
      border: 3px solid var(--success-color) !important;
    }

    &:hover {
      cursor: pointer;
      border: 1px solid var(--active-color);
    }

    &.active {
      border: 2px solid var(--active-color) !important;
    }
  }

  .img-wrapper {
    min-height: 119px;
  }

  .file-wrapper {
    width: 100%;
  }

  .cdk-drop-list {
    min-height: 180px;
    flex-wrap: wrap;
  }

  .photos-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    padding: 15px;
  }

  // Native drag and drop styles for internal photos
  .draggable-item {
    cursor: grab;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      transform: scale(1.02);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &:active {
      cursor: grabbing;
    }

    &.dragging {
      opacity: 0.5;
      transform: scale(0.95);
    }

    &.drag-over {
      border: 2px dashed #007bff;
      background: rgba(0, 123, 255, 0.1);
      transform: scale(1.05);
      box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
    }

    // Add a visual indicator for draggable items
    &::before {
      content: '⋮⋮';
      position: absolute;
      top: 5px;
      right: 5px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 12px;
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: 10;
      pointer-events: none;
    }

    &:hover::before {
      opacity: 1;
    }
  }

  // Empty drop zone for when no photos exist
  .empty-drop-zone {
    min-height: 150px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 15px;
    transition: all 0.3s ease;
    background: #f8f9fa;

    &.drag-over {
      border-color: #007bff;
      background: rgba(0, 123, 255, 0.05);
      transform: scale(1.02);
    }

    .drop-zone-content {
      text-align: center;
      color: #6c757d;

      i {
        font-size: 2rem;
        margin-bottom: 8px;
        display: block;
      }

      span {
        font-size: 14px;
        font-weight: 500;
      }
    }

    @media only screen and (max-width: 768px) {
      min-height: 120px;
      margin: 10px;

      .drop-zone-content {
        i {
          font-size: 1.5rem;
        }

        span {
          font-size: 12px;
        }
      }
    }
  }

  // Drop zone indicators between photos
  .drop-zone-indicator {
    width: 4px;
    height: 150px;
    margin: 0 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    opacity: 0;

    @media only screen and (max-width: 768px) {
      height: 120px;
      margin: 0 6px;
    }

    @media only screen and (max-width: 500px) {
      height: 100px;
      margin: 0 4px;
    }

    .drop-line {
      width: 4px;
      height: 100%;
      background: transparent;
      border-radius: 2px;
      transition: all 0.3s ease;
    }

    &.active {
      opacity: 1;

      .drop-line {
        background: #007bff;
        box-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
        animation: pulse 1s infinite;
      }
    }
  }

  // Zone highlighting for cross-category drops
  &.drag-over-zone {
    background: rgba(0, 123, 255, 0.05);
    border: 2px dashed #007bff;
    border-radius: 8px;
    transition: all 0.3s ease;

    .drop-zone-indicator {
      opacity: 0.3;

      .drop-line {
        background: #007bff;
      }
    }
  }

  .photo-item {
    width: 200px;
    height: 150px;
    position: relative;
    border-radius: 8px;
    overflow: hidden;

    @media only screen and (max-width: 768px) {
      width: 150px;
      height: 120px;
    }

    @media only screen and (max-width: 500px) {
      width: 120px;
      height: 100px;
    }
  }

  // Native drag and drop styles
  .draggable-item {
    cursor: grab;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      transform: scale(1.02);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &:active {
      cursor: grabbing;
    }

    &.dragging {
      opacity: 0.5;
      transform: scale(0.95);
    }

    &.drag-over {
      border: 2px dashed #007bff;
      background: rgba(0, 123, 255, 0.1);
      transform: scale(1.05);
      box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
    }

    // Add a visual indicator for draggable items
    &::before {
      content: '⋮⋮';
      position: absolute;
      top: 5px;
      right: 5px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 12px;
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: 10;
      pointer-events: none;
    }

    &:hover::before {
      opacity: 1;
    }
  }

  // Empty drop zone for when no photos exist
  .empty-drop-zone {
    min-height: 150px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 15px;
    transition: all 0.3s ease;
    background: #f8f9fa;

    &.drag-over {
      border-color: #007bff;
      background: rgba(0, 123, 255, 0.05);
      transform: scale(1.02);
    }

    .drop-zone-content {
      text-align: center;
      color: #6c757d;

      i {
        font-size: 2rem;
        margin-bottom: 8px;
        display: block;
      }

      span {
        font-size: 14px;
        font-weight: 500;
      }
    }

    @media only screen and (max-width: 768px) {
      min-height: 120px;
      margin: 10px;

      .drop-zone-content {
        i {
          font-size: 1.5rem;
        }

        span {
          font-size: 12px;
        }
      }
    }
  }

  // Drop zone indicators between photos
  .drop-zone-indicator {
    width: 4px;
    height: 150px;
    margin: 0 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    opacity: 0;

    @media only screen and (max-width: 768px) {
      height: 120px;
      margin: 0 6px;
    }

    @media only screen and (max-width: 500px) {
      height: 100px;
      margin: 0 4px;
    }

    .drop-line {
      width: 4px;
      height: 100%;
      background: transparent;
      border-radius: 2px;
      transition: all 0.3s ease;
    }

    &.active {
      opacity: 1;

      .drop-line {
        background: #007bff;
        box-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
        animation: pulse 1s infinite;
      }
    }
  }

  // Zone highlighting for cross-category drops
  &.drag-over-zone {
    background: rgba(0, 123, 255, 0.05);
    border: 2px dashed #007bff;
    border-radius: 8px;
    transition: all 0.3s ease;

    .drop-zone-indicator {
      opacity: 0.3;

      .drop-line {
        background: #007bff;
      }
    }
  }

  @keyframes pulse {
    0% {
      transform: scaleY(1);
      opacity: 1;
    }
    50% {
      transform: scaleY(1.1);
      opacity: 0.7;
    }
    100% {
      transform: scaleY(1);
      opacity: 1;
    }
  }
}

.drop-zone {
  .title,
  .subtitle {
    font-size: 11px;
    line-height: 1;
  }
}

.header-span {
  color: var(--active-color) !important;
  font-size: 17px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 27px;
}

.photos-switch {
  // Toggle button css
  .switch {
    position: relative;
    display: inline-block;
    width: 35px;
    height: 19px;
  }

  .switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: 0.4s;
    transition: 0.4s;
  }

  .slider:before {
    position: absolute;
    content: '';
    height: 15px;
    width: 15px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: 0.4s;
  }

  input:checked + .slider {
    background-color: var(--success-color);
  }

  input:focus + .slider {
    box-shadow: 0 0 1px var(--success-color);
  }

  input:checked + .slider:before {
    -webkit-transform: translateX(16px);
    -ms-transform: translateX(16px);
    transform: translateX(16px);
  }

  /* Rounded sliders */
  .slider.round {
    border-radius: 34px;
  }

  .slider.round:before {
    border-radius: 50%;
  }

  .switch {
    border: none;
  }

  .toggle-label {
    margin: 0px 5px 0px 5px;
  }
}

.toggle-label {
  font-size: 16px !important;
  font-weight: 600 !important;
}

::ng-deep .inventory-photos {
  .p-panel .p-panel-header {
    border-top: none;
    border-left: none;
    border-right: none;
  }

  .p-panel .p-panel-content {
    border-bottom: none;
    border-left: none;
    border-right: none;
  }
}

.file-progress {
  position: absolute;
  bottom: 0px;
  width: 100%;

  .p-progressbar {
    border-radius: 0;
    height: 20px;
  }

  .p-progressbar-label {
    font-size: 13px;
  }
}

.file-box-wrapper {
  position: relative;
}

.files {
  width: 200px;
  height: 150px;
  margin: 0px 5px;

  .img-wrapper {
    width: auto;
  }
}

.img-checkbox-display-picture {
  bottom: 5px;
  left: 5px;
  position: absolute;

  ::ng-deep .p-checkbox .p-checkbox-box {
    border-radius: 50%;
    width: 17px;
    height: 17px;
  }
}

.preview-picture {
  background-color: var(--white-color);
}

.rectangle-green {
  height: 25px;
  width: 35px;
  border-radius: 12.5px;
  background-color: var(--success-color);
  margin-left: 10px;
  text-align: center;
  color: var(--white-color);
}

.rectangle-gray {
  height: 25px;
  width: 35px;
  border-radius: 12.5px;
  background-color: $gray-rectangle-color;
  margin-left: 10px;
  text-align: center;
  color: var(--white-color);
}

.public-photos,
.internal-photos {
  display: flex;
}

.ml-5 {
  margin-left: 5px;
}

.close-icon {
  position: absolute;
  right: 0;
  padding: 15px;
  font-size: 25px;
}

::ng-deep .inventory-photos .p-component-overlay {
  background-color: rgb(0, 0, 0, 0.4) !important;

  .p-dialog-content {
    padding: 0 !important;
  }
}

.view-zoomed-image {
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 700px;
}

::ng-deep .photos-close-icon {
  svg {
    background-color: white;
    padding: 2px 9px;
  }
}

.btn-p {
  padding: 5px !important;
}

.image-main-wrapper {
  min-width: calc(100% - 154px);
}



.cdk-drop-list {
  width: 100%;
  z-index: 25;
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1);
  transform: rotate(5deg);
  opacity: 0.9;
}



.show-drop-zone,
.drag-active {
  border: 2px dashed #007bff;
  background-color: rgba(0, 123, 255, 0.05);
  border-radius: 8px;
  transition: all 200ms ease;

  &::after {
    content: 'Drop images here to reorder';
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    color: #007bff;
    font-weight: 500;
    font-size: 12px;
    pointer-events: none;
    z-index: 10;
    background: rgba(255, 255, 255, 0.9);
    padding: 4px 8px;
    border-radius: 4px;
  }
}

.drag-active {
  border-color: #28a745;
  background-color: rgba(40, 167, 69, 0.05);

  &::after {
    color: #28a745;
    content: 'Drop here';
  }
}



  .accordion-header {
    flex-direction: column;

    .public-photos,
    .internal-photos {
      margin-bottom: 10px;
    }
  }

  .switch {
    margin-right: 10px;
  }

  .cdk-drop-list {
    justify-content: center;
  }


@media only screen and (max-width: 500px) {
  .view-zoomed-image {
    width: 360px;
  }
}
