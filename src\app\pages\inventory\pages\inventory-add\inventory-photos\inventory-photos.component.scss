@import 'src/assets/scss/variables';

.inventory-images-public {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: inherit;

  .img-wrapper,
  .img-wrapper img {
    height: 150px;
    width: 200px;
    border-radius: 3px;
    position: relative;

    &.highlight {
      border: 3px solid var(--success-color) !important;
      min-height: 156px;
    }

    &.highlights {
      border: 3px solid var(--success-color) !important;
    }

    &:hover {
      cursor: pointer;
      border: 1px solid var(--active-color);
    }

    &.highlights {
      border: 3px solid var(--success-color) !important;
    }

    &.active {
      border: 2px solid var(--active-color) !important;
    }
  }

  .img-wrapper {
    min-height: 119px;
  }

  .file-wrapper {
    width: 100%;
  }

  .cdk-drop-list {
    min-height: 180px;
    flex-wrap: wrap;
  }

  .photos-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    width: 100%;
    min-height: 180px;
    padding: 15px;
    border: 2px dashed transparent;
    border-radius: 8px;
    transition: all 0.3s ease;

    &.cdk-drop-list-dragging {
      border-color: #007bff;
      background-color: rgba(0, 123, 255, 0.05);
    }
  }

  .photo-item {
    width: 200px;
    height: 150px;
    cursor: grab;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &:active {
      cursor: grabbing;
    }

    &.cdk-drag-dragging {
      opacity: 0.5;
    }
  }

  .photo-placeholder {
    width: 200px;
    height: 150px;
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;

    &::before {
      content: 'Drop here';
      color: #6c757d;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .img-wrapper:not(.drop-zone) {
    border: 1px solid transparent;
  }

  .photo-detail {
    font-size: 13px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 20px;
    height: 30px;
    padding-left: 5px;
    padding-right: 5px;
  }

  .photo-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
  }

  .delete-icon {
    position: absolute;
    top: 5px;
    right: 70px;
    padding: 4px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;

    .pi {
      font-size: 12px;
      color: red;
      margin: 3px;
      background-color: var(--white-color);
      border-radius: 50%;
      padding: 5px;
    }

    &:hover {
      .pi {
        font-size: 13px;
      }
    }
  }

  .img-checkbox {
    top: 5px;
    left: 5px;
    position: absolute;

    ::ng-deep .p-checkbox .p-checkbox-box {
      border-radius: 50%;
      width: 17px;
      height: 17px;
    }
  }
}

.inventory-images-internal {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: inherit;

  .img-wrapper,
  .img-wrapper img {
    height: 150px;
    width: 200px;
    border-radius: 3px;
    position: relative;

    &:hover {
      cursor: pointer;
      border: 1px solid var(--active-color);
    }

    &.active {
      border: 2px solid var(--active-color) !important;
    }
  }

  .img-wrapper {
    min-height: 119px;
  }

  .img-wrapper:not(.drop-zone) {
    border: 1px solid transparent;
  }

  .photo-detail {
    font-size: 13px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 20px;
    height: 30px;
    padding-left: 5px;
    padding-right: 5px;
  }

  .file-wrapper {
    width: 100%;
  }

  .cdk-drop-list {
    min-height: 180px;
    flex-wrap: wrap;
  }

  // Internal photos use the same styling as public photos

  .photo-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
  }

  .delete-icon {
    position: absolute;
    top: 5px;
    right: 70px;
    padding: 4px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;

    .pi {
      font-size: 12px;
      color: red;
      margin: 3px;
      background-color: var(--white-color);
      border-radius: 50%;
      padding: 5px;
    }

    &:hover {
      .pi {
        font-size: 13px;
      }
    }
  }

  .img-checkbox {
    top: 5px;
    left: 5px;
    position: absolute;

    ::ng-deep .p-checkbox .p-checkbox-box {
      border-radius: 50%;
      width: 17px;
      height: 17px;
    }
  }
}

.drop-zone {
  .title,
  .subtitle {
    font-size: 11px;
    line-height: 1;
  }
}

.header-span {
  color: var(--active-color) !important;
  font-size: 17px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 27px;
}

.photos-switch {
  // Toggle button css
  .switch {
    position: relative;
    display: inline-block;
    width: 35px;
    height: 19px;
  }

  .switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: 0.4s;
    transition: 0.4s;
  }

  .slider:before {
    position: absolute;
    content: '';
    height: 15px;
    width: 15px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: 0.4s;
  }

  input:checked + .slider {
    background-color: var(--success-color);
  }

  input:focus + .slider {
    box-shadow: 0 0 1px var(--success-color);
  }

  input:checked + .slider:before {
    -webkit-transform: translateX(16px);
    -ms-transform: translateX(16px);
    transform: translateX(16px);
  }

  /* Rounded sliders */
  .slider.round {
    border-radius: 34px;
  }

  .slider.round:before {
    border-radius: 50%;
  }

  .switch {
    border: none;
  }

  .toggle-label {
    margin: 0px 5px 0px 5px;
  }
}

.toggle-label {
  font-size: 16px !important;
  font-weight: 600 !important;
}

::ng-deep .inventory-photos {
  .p-panel .p-panel-header {
    border-top: none;
    border-left: none;
    border-right: none;
  }

  .p-panel .p-panel-content {
    border-bottom: none;
    border-left: none;
    border-right: none;
  }
}

.file-progress {
  position: absolute;
  bottom: 0px;
  width: 100%;

  .p-progressbar {
    border-radius: 0;
    height: 20px;
  }

  .p-progressbar-label {
    font-size: 13px;
  }
}

.file-box-wrapper {
  position: relative;
}

.files {
  width: 200px;
  height: 150px;
  margin: 0px 5px;

  .img-wrapper {
    width: auto;
  }
}

.img-checkbox-display-picture {
  bottom: 5px;
  left: 5px;
  position: absolute;

  ::ng-deep .p-checkbox .p-checkbox-box {
    border-radius: 50%;
    width: 17px;
    height: 17px;
  }
}

.preview-picture {
  background-color: var(--white-color);
}

.rectangle-green {
  height: 25px;
  width: 35px;
  border-radius: 12.5px;
  background-color: var(--success-color);
  margin-left: 10px;
  text-align: center;
  color: var(--white-color);
}

.rectangle-gray {
  height: 25px;
  width: 35px;
  border-radius: 12.5px;
  background-color: $gray-rectangle-color;
  margin-left: 10px;
  text-align: center;
  color: var(--white-color);
}

.public-photos,
.internal-photos {
  display: flex;
}

.ml-5 {
  margin-left: 5px;
}

.close-icon {
  position: absolute;
  right: 0;
  padding: 15px;
  font-size: 25px;
}

::ng-deep .inventory-photos .p-component-overlay {
  background-color: rgb(0, 0, 0, 0.4) !important;

  .p-dialog-content {
    padding: 0 !important;
  }
}

.view-zoomed-image {
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 700px;
}

::ng-deep .photos-close-icon {
  svg {
    background-color: white;
    padding: 2px 9px;
  }
}

.btn-p {
  padding: 5px !important;
}

.image-main-wrapper {
  min-width: calc(100% - 154px);
}



.cdk-drop-list {
  width: 100%;
  z-index: 25;
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1);
  transform: rotate(5deg);
  opacity: 0.9;
}

// CDK Drag and Drop Global Styling
.cdk-drag-preview {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  transform: rotate(3deg);
  opacity: 0.9;
  z-index: 1000;

  .photo-item {
    box-shadow: none;
    transform: none;
  }
}

.cdk-drag-placeholder {
  opacity: 0.4;
  background: #e9ecef;
  border: 2px dashed #adb5bd;
  border-radius: 8px;
  width: 200px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;

  &::before {
    content: '⇅ Drop here';
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
  }
}

.cdk-drag-animating {
  transition: transform 300ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

.cdk-drop-list {
  &.cdk-drop-list-receiving {
    border-color: #28a745 !important;
    background-color: rgba(40, 167, 69, 0.1) !important;
  }
}

.show-drop-zone,
.drag-active {
  border: 2px dashed #007bff;
  background-color: rgba(0, 123, 255, 0.05);
  border-radius: 8px;
  transition: all 200ms ease;

  &::after {
    content: 'Drop images here to reorder';
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    color: #007bff;
    font-weight: 500;
    font-size: 12px;
    pointer-events: none;
    z-index: 10;
    background: rgba(255, 255, 255, 0.9);
    padding: 4px 8px;
    border-radius: 4px;
  }
}

.drag-active {
  border-color: #28a745;
  background-color: rgba(40, 167, 69, 0.05);

  &::after {
    color: #28a745;
    content: 'Drop here';
  }
}

// Responsive adjustments
@media only screen and (max-width: 768px) {
  .photo-item {
    width: 150px;
    height: 120px;
  }

  .photo-placeholder,
  .cdk-drag-placeholder {
    width: 150px;
    height: 120px;
  }

  .photos-grid {
    gap: 10px;
    padding: 10px;
  }
}

@media only screen and (max-width: 500px) {
  .photo-item {
    width: 120px;
    height: 100px;
  }

  .photo-placeholder,
  .cdk-drag-placeholder {
    width: 120px;
    height: 100px;
  }

  .photos-grid {
    gap: 8px;
    padding: 8px;
  }

  .accordion-header {
    flex-direction: column;

    .public-photos,
    .internal-photos {
      margin-bottom: 10px;
    }
  }

  .switch {
    margin-right: 10px;
  }

  .cdk-drop-list {
    justify-content: center;
  }
}

@media only screen and (max-width: 500px) {
  .view-zoomed-image {
    width: 360px;
  }
}
