import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormArray, FormBuilder } from '@angular/forms';
import { Constants, MESSAGES, icons } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { Account } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { InventoryListItem, InventoryPhotoItem, InventoryPhotoType, UnitImagesResponse } from '@pages/inventory/models';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { IncomingTruckBoardListItem } from '@pages/transport/models/incoming-truck.model';
import * as saveAs from 'file-saver';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { FileProperties, FileUploadProgress } from 'src/app/@shared/models';
import { FileUploadService } from 'src/app/@shared/services/file-upload.service';

@Component({
  selector: 'app-inventory-photos',
  templateUrl: './inventory-photos.component.html',
  styleUrls: ['./inventory-photos.component.scss'],
  changeDetection: ChangeDetectionStrategy.Default
})
export class InventoryPhotosComponent extends BaseComponent implements OnInit, OnChanges {

  inventoryPhotosFormGroup!: FormArray;
  inventoryPhotos: InventoryPhotoItem[] = [];
  focusedImage!: InventoryPhotoItem | null;
  selectedPublicPhotos: InventoryPhotoItem[] = [];
  selectedInternalPhotos: InventoryPhotoItem[] = [];
  fileUploadProgressesPublic: FileUploadProgress[] = [];
  fileUploadProgressesInternal: FileUploadProgress[] = [];
  isPublicFileUploadProgress!: boolean;
  focusUploadedImage!: FileUploadProgress | null;
  currentUser!: Account | null;
  unitId!: number | undefined | null;
  imageDetails!: InventoryPhotoItem | undefined;
  inventoryPhotoType = InventoryPhotoType;
  taskFileUploadPath = 'Inventory Photos';
  SelectAll = "Select All";
  selectDelectLabelPublic = this.SelectAll;
  selectDelectLabelInternal = this.SelectAll;
  selectedPublicPhotosCount = 0;
  selectedInternalPhotosCount = 0;
  isPublicPhotosSelectAll = false;
  isInternalPhotosSelectAll = false;

  // Drag and drop state
  draggedIndex: number = -1;
  draggedType: string = '';
  draggedPhoto: InventoryPhotoItem | null = null;
  dragOverZone: string = '';
  dragOverPosition: string = '';
  isEditMode = false;
  isDisplayPictureSet = false;
  accordionTabs = {
    publicPhotos: true,
    internalPhotos: true
  }
  showEnlargedImage = false;
  showPhotoDetails = false;
  viewImageScr?: string;
  @Input() isViewMode!: boolean;
  @Input() inventoryInfo!: InventoryListItem | null;
  @Input() inventoryIncomingInfo!: IncomingTruckBoardListItem | null;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() onChangeActiveIndex: EventEmitter<boolean> = new EventEmitter<boolean>();
  publicPhotos: any[] = [];
  internalPhotos: any[] = [];
  dragStartedToTransferPhoto = false;
  temporaryIdForNewlyUploadedPhoto = 1;

  constructor(private readonly inventoryService: InventoryService,
    private readonly toasterService: AppToasterService,
    private readonly authService: AuthService,
    private readonly fileUploadService: FileUploadService,
    private readonly fb: FormBuilder,
    private readonly confirmationService: ConfirmationService,
    private readonly cdf: ChangeDetectorRef,
    private readonly commonSharedService: CommonSharedService) {
    super();
  }

  ngOnInit(): void {
    this.initializeFormGroup();
    this.getCurrentUser();
    this.getUnitId();
    if (this.isEditMode) {
      this.getInventoryPhotosById();
    }
  }

  getUnitId() {
    if (this.isEditMode) {
      this.unitId = this.inventoryInfo ? this.inventoryInfo?.id : this.inventoryIncomingInfo?.unitId;
    }
    else {
      this.inventoryService.getUnitId$().pipe(takeUntil(this.destroy$)).subscribe(res => {
        this.unitId = res;
      });
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.inventoryInfo?.currentValue || changes.inventoryIncomingInfo?.currentValue) {
      this.isEditMode = true;
    }
  }

  private initializeFormGroup(): void {
    this.inventoryPhotosFormGroup = this.inventoryPhotosFormArray;
  }

  get inventoryPhotosFormArray(): FormArray {
    return this.fb.array([])
  }

  private getInventoryPhotosById() {
    this.inventoryPhotosFormGroup.clear();
    this.isLoading = true;
    const unitId = this.inventoryIncomingInfo ? this.inventoryIncomingInfo?.unitId : this.inventoryInfo?.id;
    const endpoint = API_URL_UTIL.inventory.inventoryPhotos.replace(':unitId', String(unitId));
    this.inventoryService.get<InventoryPhotoItem[]>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (photos) => {
        this.inventoryPhotosFormGroup.clear();
        for (const photo of photos) {
          if (photo.photoType === InventoryPhotoType.INTERNAL_PHOTO || photo.photoType === InventoryPhotoType.PUBLIC_PHOTO) {
            if (photo.displayPicture) {
              this.isDisplayPictureSet = true;
            }
            this.inventoryPhotosFormGroup.push(this.fb.group({
              id: photo.id,
              url: photo.url,
              detail: photo.detail,
              photoType: photo.photoType,
              unitId: photo.unitId,
              fileUrl: photo.fullUrl,
              displayPicture: photo.displayPicture,
              isChecked: false,
              imageOrder: photo.imageOrder
            }));
          }
          this.cdf.detectChanges();
          // TODO: Not need currently but used in past so maybe required in future
          // const data: any = this.inventoryPhotosFormGroup.value.filter((i: any) => i.displayPicture === true);
          this.publicPhotos = this.inventoryPhotosFormGroup.value.filter((i: any) => i.photoType === "PUBLIC");
          this.internalPhotos = this.inventoryPhotosFormGroup.value.filter((i: any) => i.photoType === "INTERNAL");
          // TODO: Not need currently but used in past so maybe required in future
          // if (data.length > 0) {
          //   const fromObject = this.inventoryPhotosFormGroup.value.findIndex((obj: any) => obj.id === data[0].id)
          //   if (fromObject > -1) {
          //     this.inventoryPhotosFormGroup.value.splice(fromObject, 1);
          //   }
          //   this.inventoryPhotosFormGroup.value.splice(0, 0, data[0])
          // }
          this.publicPhotos.sort((a, b) => a.imageOrder - b.imageOrder);
          this.internalPhotos.sort((a, b) => a.imageOrder - b.imageOrder);
        }

        // TODO: Not need currently but used in past so maybe required in future
        // const data: any = this.inventoryPhotosFormGroup.value.filter((i: any) => i.displayPicture === true);

        // if (data.length > 0) {
        //   const fromObject = this.inventoryPhotosFormGroup.value.findIndex((obj: any) => obj.id === data[0].id)
        //   if (fromObject > -1) {
        //     this.inventoryPhotosFormGroup.value.splice(fromObject, 1);
        //   }
        //   this.inventoryPhotosFormGroup.value.splice(0, 1, data[0])
        // }
        this.setPhotosCount();
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
      }
    });
  }

  get isFileUploadInProgressPublic(): boolean {
    if (this.fileUploadProgressesPublic.some(fileProgress => fileProgress.progress$.getValue() < 100)) {
      return true;
    }
    return false;
  }

  get isFileUploadProgressesInternal(): boolean {
    if (this.fileUploadProgressesInternal.some(fileProgress => fileProgress.progress$.getValue() < 100)) {
      return true;
    }
    return false;
  }

  onSubmit(close = true, unitId = 0): void {
    if (this.isFileUploadInProgressPublic) {
      this.toasterService.warning(MESSAGES.fileUploadInProgress);
      return;
    }
    this.savePhotos(close, unitId);
  }

  private inventoryPhotosParams(unitId = 0): UnitImagesResponse[] {
    const inventoryPhotosArray: UnitImagesResponse[] = [];
    for (const inventoryPhotos of this.inventoryPhotosFormGroup.value) {
      if (inventoryPhotos.isNewUpload) {
        inventoryPhotosArray.push({
          id: inventoryPhotos.id,
          url: inventoryPhotos.url,
          detail: inventoryPhotos.detail,
          photoType: inventoryPhotos.photoType,
          unitId: unitId,
          fullUrl: inventoryPhotos.fullUrl,
          displayPicture: inventoryPhotos.displayPicture
        });
      }
    }
    for (const publicPhoto of this.publicPhotos) {
      if (publicPhoto.binary) {
        inventoryPhotosArray.push({
          id: null,
          url: publicPhoto.url,
          detail: publicPhoto.detail,
          photoType: InventoryPhotoType.PUBLIC_PHOTO,
          unitId: unitId ? unitId : publicPhoto.unitId,
          fullUrl: publicPhoto.binary,
          displayPicture: publicPhoto.displayPicture,
          imageOrder: publicPhoto.imageOrder
        });
      } else if (publicPhoto.photoType === this.inventoryPhotoType.INTERNAL_PHOTO) {
        publicPhoto.imageOrder = publicPhoto.imageOrder;
        publicPhoto.photoType = this.inventoryPhotoType.PUBLIC_PHOTO;
        inventoryPhotosArray.push(publicPhoto);
      } else {
        inventoryPhotosArray.push(publicPhoto);
      }
    }
    for (const internalPhoto of this.internalPhotos) {
      if (internalPhoto.binary) {
        inventoryPhotosArray.push({
          id: null,
          url: internalPhoto.url,
          detail: internalPhoto.detail,
          photoType: InventoryPhotoType.INTERNAL_PHOTO,
          unitId: unitId ? unitId : internalPhoto.unitId,
          fullUrl: internalPhoto.binary,
          displayPicture: false,
          imageOrder: internalPhoto.imageOrder
        });
      } else if (internalPhoto.photoType === this.inventoryPhotoType.PUBLIC_PHOTO) {
        internalPhoto.imageOrder = internalPhoto.imageOrder;
        internalPhoto.photoType = this.inventoryPhotoType.INTERNAL_PHOTO;
        inventoryPhotosArray.push(internalPhoto);
      } else {
        inventoryPhotosArray.push(internalPhoto);
      }


    }
    return inventoryPhotosArray;
  }

  savePhotos(close = true, unitId = 0): void {
    this.commonSharedService.setBlockUI$(true);
    const inventoryPhotosParams = this.inventoryPhotosParams(unitId)
    this.inventoryService.add(inventoryPhotosParams, (API_URL_UTIL.inventory.images))
      .subscribe({
        next: () => {
          this.inventoryService.setImageUploaded(true);
          if (!close) {
            this.onChangeActiveIndex.next(true);
            this.commonSharedService.setBlockUI$(false);
          }
        },
        error: () => {
          this.commonSharedService.setBlockUI$(false);
        }
      });
  }

  onViewPhotoDetail(imageDetails: InventoryPhotoItem): void {
    this.showPhotoDetails = true;
    this.imageDetails = imageDetails;
  }

  showNextImage(imagePhotos: InventoryPhotoItem): void {
    if (imagePhotos.photoType === InventoryPhotoType.PUBLIC_PHOTO) {
      const selectPhotoIndex = imagePhotos.binary ?
        this.publicPhotos.findIndex(publicPhoto => publicPhoto.tempId === imagePhotos.tempId)
        : this.publicPhotos.findIndex(publicPhoto => publicPhoto.id === imagePhotos.id);
      this.imageDetails = this.publicPhotos[selectPhotoIndex + 1];
    } else {
      const selectPhotoIndex = this.internalPhotos.findIndex(publicPhoto => publicPhoto.id === imagePhotos.id);
      this.imageDetails = this.internalPhotos[selectPhotoIndex + 1];
    }
  }

  showPreviousImage(imagePhotos: InventoryPhotoItem): void {
    if (imagePhotos.photoType === InventoryPhotoType.PUBLIC_PHOTO) {
      const selectPhotoIndex = imagePhotos.binary ?
        this.publicPhotos.findIndex(publicPhoto => publicPhoto.tempId === imagePhotos.tempId)
        : this.publicPhotos.findIndex(publicPhoto => publicPhoto.id === imagePhotos.id);
      this.imageDetails = this.publicPhotos[selectPhotoIndex - 1];
    } else {
      const selectPhotoIndex = this.internalPhotos.findIndex(publicPhoto => publicPhoto.id === imagePhotos.id);
      this.imageDetails = this.internalPhotos[selectPhotoIndex - 1];
    }
  }

  onSelectPublicPhoto(img: InventoryPhotoItem): void {
    console.log("onSelectPublicPhoto", img);
    const photoIndex = this.selectedPublicPhotos.findIndex(x => x.id === img.id);
    console.log("photoIndex", photoIndex);
    if (photoIndex === -1) {
      this.selectedPublicPhotos.push(img);
    } else {
      this.selectedPublicPhotos.splice(photoIndex, 1);
    }
  }

  onSelectInternalPhoto(img: InventoryPhotoItem): void {
    console.log("onSelectInternalPhoto", img);
    const photoIndex = this.selectedInternalPhotos.findIndex(x => x.id === img.id);
    if (photoIndex > -1) {
      this.selectedInternalPhotos.push(img);
    } else {
      this.selectedInternalPhotos.splice(photoIndex, 1);
    }
  }

  isPublicPhotoSelected(img: InventoryPhotoItem): boolean {
    return this.selectedPublicPhotos.findIndex(i => i.id === img.id) > -1;
  }

  isInternalPhotoSelected(img: InventoryPhotoItem): boolean {
    return this.selectedInternalPhotos.findIndex(i => i.id === img.id) > -1;
  }

  onFileSelect(event: any, isUploadImagePublic: boolean) {
    const filesShouldBeUploaded = []
    for (const file of event.target.files) {
      if (file.size > this.constants.fileSize) {
        this.toasterService.warning(MESSAGES.fileUploadMessage)
        return
      }
      else {
        if (event.target?.files?.length) {
          const extensionDot = file?.name?.lastIndexOf('.');
          const ext = file?.name?.substring(extensionDot + 1).toLowerCase();
          if (!Constants.allowedImgFormats.includes(ext)) {
            this.toasterService.error(MESSAGES.fileTypeNotSupported);
            return;
          }
          filesShouldBeUploaded.push(file)
        }
      }
    }
    this.initUploadImage(filesShouldBeUploaded, isUploadImagePublic);
  }

  initUploadImage(filesShouldBeUploaded: Array<File>, isUploadImagePublic: boolean): void {
    this.isPublicFileUploadProgress = isUploadImagePublic;
    const formData: FormData = new FormData();

    for (const file of filesShouldBeUploaded) {
      formData.append('multipartFiles', file, `${file.name}`);
      const uploadProgress = new FileUploadProgress();
      uploadProgress.index = isUploadImagePublic ? this.fileUploadProgressesPublic.length : this.fileUploadProgressesInternal.length;
      uploadProgress.file = file;
      isUploadImagePublic ? this.fileUploadProgressesPublic.push(uploadProgress) : this.fileUploadProgressesInternal.push(uploadProgress);
      this.fileUploadService.setFileUploads(isUploadImagePublic ? this.fileUploadProgressesPublic : this.fileUploadProgressesInternal);
      this.setFileUrl(file, uploadProgress);
    }

    const progress = isUploadImagePublic ? this.fileUploadProgressesPublic : this.fileUploadProgressesInternal;
    this.imageUpload(formData, progress, isUploadImagePublic);

  }

  imageUpload(formData: FormData, progress: Array<FileUploadProgress>, isUploadImagePublic: boolean): void {
    this.fileUploadService.uploadFiles(formData, `${this.currentUser?.id}`, this.taskFileUploadPath,
      progress)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (fileUrls: Array<{ url: string }>) => {
          this.afterImageUploaded(fileUrls, isUploadImagePublic)
        },
        error: () => {
          this.toasterService.error(MESSAGES.fileUploadError);
          isUploadImagePublic ? this.fileUploadProgressesPublic.splice(this.fileUploadProgressesPublic.length - 1, 1) : this.fileUploadProgressesInternal.splice(this.fileUploadProgressesPublic.length - 1, 1);
          this.cdf.detectChanges();
        }
      });
  }

  afterImageUploaded(fileUrls: Array<{ url: string }>, isUploadImagePublic: boolean): void {
    this.fileUploadService.setFileUploads(isUploadImagePublic ? this.fileUploadProgressesPublic : this.fileUploadProgressesInternal);
    for (const [index, fileUrl] of fileUrls.entries()) {
      const photoObject = {
        id: null,
        url: fileUrl.url,
        detail: null,
        photoType: isUploadImagePublic ? InventoryPhotoType.PUBLIC_PHOTO : InventoryPhotoType.INTERNAL_PHOTO,
        unitId: this.unitId,
        fullUrl: null,
        displayPicture: false,
        binary: isUploadImagePublic ? this.fileUploadProgressesPublic[index].fileProperty?.fileUrl : this.fileUploadProgressesInternal[index].fileProperty?.fileUrl,
        tempId: this.temporaryIdForNewlyUploadedPhoto,
        imageOrder: isUploadImagePublic ? this.publicPhotos.length : this.internalPhotos.length
      };
      if (isUploadImagePublic) {
        if (this.publicPhotos?.length === 0) {
          photoObject.displayPicture = true;
        }
        this.publicPhotos.push(photoObject);
        this.selectedPublicPhotosCount++;
      } else {
        this.internalPhotos.push(photoObject);
        this.selectedInternalPhotosCount++;
      }
      this.temporaryIdForNewlyUploadedPhoto += 1;
    }
    // TODO will be remove once complete
    // if (this.publicPhotos?.length) {
    //   for (let i = 0;  i < this.publicPhotos.length; i++) {
    //   this.publicPhotos[i].imageOrder = i;
    //   }
    // }
    this.fileUploadProgressesPublic = this.fileUploadProgressesInternal = [];
    this.toasterService.success(MESSAGES.fileUploadSuccess);
    this.cdf.detectChanges();
  }

  private setFileUrl(file: File, uploadProgress: FileUploadProgress): void {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      uploadProgress.fileProperty = new FileProperties(file.name, reader.result as any);
    }
  }

  private getCurrentUser(): void {
    this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe(user => {
      if (user) {
        this.currentUser = user;
      }
    });
  }

  onToggleChange(photoType: InventoryPhotoType): void {
    for (const photos of photoType === InventoryPhotoType.PUBLIC_PHOTO ? this.publicPhotos : this.internalPhotos) {
      this.selectPhotosByType(photos, photoType);
    }
    if (photoType === InventoryPhotoType.PUBLIC_PHOTO) {
      this.selectDelectLabelPublic = this.isPublicPhotosSelectAll ? "De-Select All" : this.SelectAll;
    } else {
      this.selectDelectLabelInternal = this.isInternalPhotosSelectAll ? "De-Select All" : this.SelectAll;
    }
  }



  selectPhotosByType(photos: InventoryPhotoItem, photoType: InventoryPhotoType) {
    if (photos.photoType === photoType) {
      const publicPhotosSelectAll = this.isPublicPhotosSelectAll ? true : false;
      const internalPhotosSelectAll = this.isInternalPhotosSelectAll ? true : false;
      photos.isSelected = photoType === InventoryPhotoType.PUBLIC_PHOTO ? publicPhotosSelectAll : internalPhotosSelectAll;
    }
  }

  private setPhotosCount() {
    this.selectedPublicPhotosCount = 0;
    this.selectedInternalPhotosCount = 0;
    for (const inventoryPhotos of this.inventoryPhotosFormGroup.value) {
      if (inventoryPhotos.photoType === InventoryPhotoType.PUBLIC_PHOTO) {
        this.selectedPublicPhotosCount = this.selectedPublicPhotosCount + 1;
      } else {
        this.selectedInternalPhotosCount = this.selectedInternalPhotosCount + 1;
      }
    }
  }

  onSelectSinglePhoto(imgInfo: InventoryPhotoItem) {
    for (const photos of this.inventoryPhotosFormGroup.value) {
      if (photos.id === imgInfo?.id) {
        photos.isSelected = photos.isSelected ? true : false;
      }
    }
  }

  trackByFile(index: number, fileProgress: any): string {
    return fileProgress.fileProperty?.fileUrl || index; // Use fileUrl or index as a unique identifier
  }

  trackByImageId(index: number, item: InventoryPhotoItem): string | number {
    return item.id || item.tempId || index;
  }

  hasSelectedPublicPhotos(): boolean {
    return this.inventoryPhotosFormGroup.value.some((photos: any) => photos.photoType === InventoryPhotoType.PUBLIC_PHOTO && photos.isSelected === true);
  }

  hasSelectedInternalPhotos(): boolean {
    return this.inventoryPhotosFormGroup.value.some((photos: any) => photos.photoType === InventoryPhotoType.INTERNAL_PHOTO && photos.isSelected === true);
  }
  onSelectNewUploadedPhoto() {
    for (const photos of this.inventoryPhotosFormGroup.value) {
      photos.isSelected = photos.isSelected ? true : false;
    }
  }
  downloadSelectedImages(inventoryPhotoType: InventoryPhotoType): void {
    for (const photos of this.inventoryPhotosFormGroup.value) {
      if (photos.isSelected && photos.photoType === inventoryPhotoType) {
        this.onDownloadServerImage(photos);
      }
    }
    for (const photos of inventoryPhotoType === InventoryPhotoType.PUBLIC_PHOTO ? this.publicPhotos : this.internalPhotos) {
      if (photos.binary && photos.isSelected) {
        this.onDownloadUploadedImage(photos.binary)
      }
    }
  }

  deleteSelectedImages(inventoryPhotoType: InventoryPhotoType): void {
    if (inventoryPhotoType === InventoryPhotoType.PUBLIC_PHOTO) {
      this.deleteSelectedPublicImages(inventoryPhotoType);
      return;
    }
    this.deleteSelectedInternalImages(inventoryPhotoType)
  }

  deleteSelectedPublicImages(inventoryPhotoType: InventoryPhotoType): void {
    const newUploadPhotos = [];
    const newUploadPhotosUrl = [];
    for (const publicPhoto of this.publicPhotos) {
      if (publicPhoto.isSelected) {
        if (publicPhoto.binary) {
          newUploadPhotos.push(publicPhoto);
          newUploadPhotosUrl.push(publicPhoto.url);
        }
        else {
          this.onDeleteConfirmation(publicPhoto.id, inventoryPhotoType);
        }
      }
    }
    this.deleteImageFromCloud(newUploadPhotosUrl, newUploadPhotos, true);
    this.selectDelectLabelPublic = this.SelectAll;
    this.isPublicPhotosSelectAll = false;
  }

  deleteSelectedInternalImages(inventoryPhotoType: InventoryPhotoType): void {
    const newUploadPhotos = [];
    const newUploadPhotosUrl = [];
    for (const internalPhoto of this.internalPhotos) {
      if (internalPhoto.isSelected) {
        if (internalPhoto.binary) {
          newUploadPhotos.push(internalPhoto);
          newUploadPhotosUrl.push(internalPhoto.url);
        }
        else {
          this.onDeleteConfirmation(internalPhoto.id, inventoryPhotoType);
        }
      }
    }
    this.deleteImageFromCloud(newUploadPhotosUrl, newUploadPhotos, false);
    this.isInternalPhotosSelectAll = false;
    this.selectDelectLabelInternal = this.SelectAll;
  }

  deleteAllSelectedImagesConfirmation(inventoryPhotoType: InventoryPhotoType, event: Event): void {
    this.confirmationService.confirm({
      header: 'Confirmation',
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'all photos'),
      icon: icons.triangle,
      accept: () => {
        this.deleteSelectedImages(inventoryPhotoType);
      }
    });
  }

  fileUploadDelete(fileDetails: any, isUploadImagePublic: boolean): void {
    this.confirmationService.confirm({
      header: 'Confirmation',
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'photo'),
      icon: icons.triangle,
      accept: () => {
        if (fileDetails.url) {
          this.deleteImageFromCloud([fileDetails.url], [fileDetails], isUploadImagePublic);
        }
      }
    });
  }

  deleteImageFromCloud(imageUrl: Array<string>, files: Array<any>, isUploadImagePublic: boolean): void {
    this.fileUploadService.deleteFile(imageUrl).pipe(takeUntil(this.destroy$)).subscribe(() => {
      for (const file of files) {
        if (isUploadImagePublic) {
          this.publicPhotos = this.publicPhotos.filter(publicPhoto => publicPhoto.tempId !== file?.tempId);
          this.selectedPublicPhotosCount--;
        } else {
          this.internalPhotos = this.internalPhotos.filter(internalPhoto => {
            return internalPhoto.tempId !== file?.tempId
          });
          this.selectedInternalPhotosCount--;
        }
        this.cdf.detectChanges();
      }
    });
  }

  private getFileName(fileUrl: string | undefined): string {
    if (!fileUrl) {
      return '';
    }
    return fileUrl.split('_').slice(1).join('_');;
  }

  onDelete(attachmentId: number | undefined, event: Event, inventoryPhotoType: InventoryPhotoType): void {
    if (!attachmentId) {
      return;
    }
    this.confirmationService.confirm({
      header: 'Confirmation',
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'photo'),
      icon: icons.triangle,
      accept: () => {
        this.onDeleteConfirmation(attachmentId, inventoryPhotoType);
      }
    });
  }

  onDeleteConfirmation(attachmentId: number, inventoryPhotoType: InventoryPhotoType): void {
    this.inventoryService.deletePhotoAttachment(attachmentId).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.photoDeleteSuccess);
      if (inventoryPhotoType === InventoryPhotoType.PUBLIC_PHOTO) {
        this.publicPhotos = this.publicPhotos.filter(publicPhoto => publicPhoto.id !== attachmentId);
        this.selectedPublicPhotosCount--;
      } else {
        this.internalPhotos = this.internalPhotos.filter(internalPhoto => {
          return internalPhoto.id !== attachmentId
        });
        this.selectedInternalPhotosCount--;
      }
      this.cdf.detectChanges();
    });
  }

  onViewImage(fileUrl: string | undefined) {
    this.showEnlargedImage = true;
    if (fileUrl) {
      this.viewImageScr = fileUrl;
    }
  }

  onDownloadUploadedImage(fileUrl: string | undefined): void {
    if (fileUrl) {
      this.utils.downloadFromUrl(fileUrl, this.getFileName(fileUrl));
    }
  }

  onDownloadServerImage(fileInfo: InventoryPhotoItem): void {
    if (fileInfo?.fileUrl) {
      this.fileUploadService.downloadFile(fileInfo.url, this.getFileName(fileInfo.url)).pipe(takeUntil(this.destroy$)).subscribe({
        next: (response: ArrayBuffer) => {
          saveAs(new Blob([response], { type: "application/image" }), this.getFileName(fileInfo.url));
        }
      });
    }
  }

  onAddEditPopupClose(hasDataBeenModified: boolean): void {
    this.showPhotoDetails = false;
    if (hasDataBeenModified) {
      this.getInventoryPhotosById();
    }
  }

  updateDetailsOfNewlyUploadedPhoto(imgInfo: InventoryPhotoItem): void {
    if (imgInfo.binary) {
      if (imgInfo?.photoType === InventoryPhotoType.PUBLIC_PHOTO) {
        for (const publicPhoto of this.publicPhotos) {
          if (imgInfo.tempId === publicPhoto.tempId) {
            publicPhoto.detail = imgInfo.detail;
          }
        }
      } else {
        for (const inventoryPhoto of this.inventoryPhotos) {
          if (imgInfo.tempId === inventoryPhoto.tempId) {
            inventoryPhoto.detail = imgInfo.detail;
          }
        }
      }
    }
  }

  displayPictureToggle(imgInfo: InventoryPhotoItem) {
    if (imgInfo.photoType === InventoryPhotoType.PUBLIC_PHOTO) {
      for (const photo of this.publicPhotos) {
        if (imgInfo.binary) {
          photo.displayPicture = imgInfo?.tempId === photo?.tempId;
        } else {
          if (photo.id === imgInfo?.id) {
            photo.displayPicture = photo.displayPicture ? photo.displayPicture : !photo.displayPicture;
            this.saveDisplayPicture(imgInfo);
          } else {
            photo.displayPicture = false;
          }
        }
      }
    }
  }

  private saveDisplayPicture(imgInfo: InventoryPhotoItem) {
    const imageId = imgInfo?.id ? imgInfo?.id.toString() : '0';
    const unitId = imgInfo?.unitId ? imgInfo?.unitId.toString() : '0';
    const endpoint = API_URL_UTIL.inventory.photoDisplay.replace(':imageId', imageId).replace(':unitId', unitId)
    this.inventoryService.patch('', endpoint).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.toasterService.success(MESSAGES.photoDisplayPicture);
    });
  }



  setImageOrder(): void {
    this.publicPhotos.forEach((image, index) => {
      image.imageOrder = index;
    });
    this.internalPhotos.forEach((image, index) => {
      image.imageOrder = index;
    });
  }

  onDragStart(event: DragEvent, index: number, type: string): void {
    console.log('🎯 Drag Start:', { index, type });
    this.draggedIndex = index;
    this.draggedType = type;

    // Store reference to the dragged photo
    if (type === 'public') {
      this.draggedPhoto = { ...this.publicPhotos[index] };
    } else {
      this.draggedPhoto = { ...this.internalPhotos[index] };
    }

    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'move';
      event.dataTransfer.setData('text/html', '');
    }

    // Add visual feedback
    const target = event.target as HTMLElement;
    target.style.opacity = '0.5';
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }

    // Add visual feedback for drop zone
    const target = event.currentTarget as HTMLElement;
    target.classList.add('drag-over');
  }

  onDrop(event: DragEvent, dropIndex: number, dropType: string): void {
    event.preventDefault();
    console.log('🎯 Drop Event:', {
      draggedIndex: this.draggedIndex,
      draggedType: this.draggedType,
      dropIndex,
      dropType
    });

    // Remove visual feedback immediately
    const target = event.currentTarget as HTMLElement;
    target.classList.remove('drag-over');

    // Skip if dropping on the same item
    if (this.draggedType === dropType && this.draggedIndex === dropIndex) {
      console.log('⏭️ Skipping - same position');
      return;
    }

    // Use setTimeout to ensure smooth UI updates
    setTimeout(() => {
      // Handle cross-category movement
      if (this.draggedType !== dropType) {
        console.log('🔄 Cross-category move:', this.draggedType, '->', dropType);
        this.movePhotoBetweenCategories(dropIndex, dropType);
      } else {
        // Handle reordering within the same category
        console.log('🔄 Same-category reorder:', dropType);
        if (dropType === 'public') {
          this.reorderPhotos(this.publicPhotos, this.draggedIndex, dropIndex);
          console.log('📋 Reordered public photos:', this.publicPhotos.map((p, i) => `${i}: ${p.id || p.tempId}`));
        } else {
          this.reorderPhotos(this.internalPhotos, this.draggedIndex, dropIndex);
          console.log('📋 Reordered internal photos:', this.internalPhotos.map((p, i) => `${i}: ${p.id || p.tempId}`));
        }
      }

      // Force change detection after the operation
      this.cdf.detectChanges();
      console.log('✅ Drop completed');
    }, 0);
  }

  onDragEnd(event: DragEvent): void {
    console.log('🎯 Drag End');

    // Reset visual feedback
    const target = event.target as HTMLElement;
    target.style.opacity = '1';

    // Remove drag-over class from all elements
    const allItems = document.querySelectorAll('.draggable-item');
    allItems.forEach(item => item.classList.remove('drag-over'));

    // Reset drag state
    this.draggedIndex = -1;
    this.draggedType = '';
    this.draggedPhoto = null;
    this.dragOverZone = '';
    this.dragOverPosition = '';
  }

  onDragOverZone(event: DragEvent, zoneType: string): void {
    event.preventDefault();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }

    this.dragOverZone = zoneType;
    console.log('🎯 Drag Over Zone:', zoneType);
  }

  onDragLeaveZone(event: DragEvent): void {
    // Only clear if we're actually leaving the zone (not entering a child element)
    const relatedTarget = event.relatedTarget as HTMLElement;
    const currentTarget = event.currentTarget as HTMLElement;

    if (!currentTarget.contains(relatedTarget)) {
      this.dragOverZone = '';
      this.dragOverPosition = '';
      console.log('🎯 Drag Leave Zone');
    }
  }

  onDragOverPosition(event: DragEvent, zoneType: string, position: number): void {
    event.preventDefault();
    event.stopPropagation();

    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }

    this.dragOverPosition = `${zoneType}-${position}`;
    console.log('🎯 Drag Over Position:', this.dragOverPosition);
  }

  onDropAtPosition(event: DragEvent, dropType: string, dropPosition: number): void {
    event.preventDefault();
    event.stopPropagation();

    console.log('🎯 Drop At Position:', {
      draggedIndex: this.draggedIndex,
      draggedType: this.draggedType,
      dropType,
      dropPosition
    });

    // Clear visual feedback
    this.dragOverZone = '';
    this.dragOverPosition = '';

    // Skip if dropping at the same position
    if (this.draggedType === dropType && this.draggedIndex === dropPosition) {
      console.log('⏭️ Skipping - same position');
      return;
    }

    // Handle the drop operation
    this.performDropOperation(dropType, dropPosition);
  }

  onDropToZone(event: DragEvent, zoneType: string): void {
    event.preventDefault();
    console.log('🎯 Drop To Zone:', {
      draggedType: this.draggedType,
      zoneType,
      draggedIndex: this.draggedIndex
    });

    // Clear zone highlighting
    this.dragOverZone = '';
    this.dragOverPosition = '';

    // Handle cross-category movement to end of list
    if (this.draggedType !== zoneType) {
      const targetArray = zoneType === 'public' ? this.publicPhotos : this.internalPhotos;
      this.performDropOperation(zoneType, targetArray.length);
    }
  }

  private performDropOperation(dropType: string, dropPosition: number): void {
    // Use setTimeout to ensure smooth UI updates
    setTimeout(() => {
      // Handle cross-category movement
      if (this.draggedType !== dropType) {
        console.log('🔄 Cross-category move:', this.draggedType, '->', dropType);
        this.movePhotoBetweenCategories(dropPosition, dropType);
      } else {
        // Handle reordering within the same category
        console.log('🔄 Same-category reorder:', dropType);

        // Adjust drop position if dragging within same array
        let adjustedPosition = dropPosition;
        if (this.draggedIndex < dropPosition) {
          adjustedPosition = dropPosition - 1;
        }

        if (dropType === 'public') {
          this.reorderPhotos(this.publicPhotos, this.draggedIndex, adjustedPosition);
          console.log('📋 Reordered public photos:', this.publicPhotos.map((p, i) => `${i}: ${p.id || p.tempId}`));
        } else {
          this.reorderPhotos(this.internalPhotos, this.draggedIndex, adjustedPosition);
          console.log('📋 Reordered internal photos:', this.internalPhotos.map((p, i) => `${i}: ${p.id || p.tempId}`));
        }
      }

      // Force change detection after the operation
      this.cdf.detectChanges();
      console.log('✅ Drop completed');
    }, 0);
  }

  private movePhotoBetweenCategories(dropIndex: number, targetType: string): void {
    if (!this.draggedPhoto) {
      console.log('❌ No dragged photo found');
      return;
    }

    console.log('🔄 Moving photo between categories:', {
      from: this.draggedType,
      to: targetType,
      draggedIndex: this.draggedIndex,
      dropIndex: dropIndex,
      photoId: this.draggedPhoto.id || this.draggedPhoto.tempId
    });

    // Create a copy of the photo for the target category
    const photoToMove = { ...this.draggedPhoto };

    // Update photo type based on target category
    if (targetType === 'public') {
      photoToMove.photoType = InventoryPhotoType.PUBLIC_PHOTO;
    } else {
      photoToMove.photoType = InventoryPhotoType.INTERNAL_PHOTO;
    }

    // Get source and target arrays
    const sourceArray = this.draggedType === 'public' ? this.publicPhotos : this.internalPhotos;
    const targetArray = targetType === 'public' ? this.publicPhotos : this.internalPhotos;

    // Remove from source array
    sourceArray.splice(this.draggedIndex, 1);
    console.log(`📤 Removed from ${this.draggedType} at index ${this.draggedIndex}`);

    // Add to target array at specified position
    const insertIndex = Math.min(dropIndex, targetArray.length);
    targetArray.splice(insertIndex, 0, photoToMove);
    console.log(`📥 Added to ${targetType} at index ${insertIndex}`);

    // Update image order for both arrays
    this.updateImageOrder(this.draggedType === 'public' ? 'PUBLIC_PHOTO' : 'INTERNAL_PHOTO');
    this.updateImageOrder(targetType === 'public' ? 'PUBLIC_PHOTO' : 'INTERNAL_PHOTO');

    console.log('📋 Final state:');
    console.log('  Public photos:', this.publicPhotos.map((p, i) => `${i}: ${p.id || p.tempId}`));
    console.log('  Internal photos:', this.internalPhotos.map((p, i) => `${i}: ${p.id || p.tempId}`));
    console.log('✅ Cross-category move completed');
  }

  private reorderPhotos(photos: InventoryPhotoItem[], fromIndex: number, toIndex: number): void {
    if (fromIndex < 0 || fromIndex >= photos.length || toIndex < 0 || toIndex >= photos.length) {
      return;
    }

    // Remove the item from the original position
    const [movedItem] = photos.splice(fromIndex, 1);

    // Insert it at the new position
    photos.splice(toIndex, 0, movedItem);

    // Update image order for all photos
    photos.forEach((photo, index) => {
      photo.imageOrder = index;
    });
  }

  updateImageOrder(photoType: string): void {
    if (photoType === this.inventoryPhotoType.PUBLIC_PHOTO) {
      this.publicPhotos.forEach((image, index) => {
        image.imageOrder = index;
      });
    } else if (photoType === this.inventoryPhotoType.INTERNAL_PHOTO) {
      this.internalPhotos.forEach((image, index) => {
        image.imageOrder = index;
      });
    }
  }
}

