<div [class]="{ 'inventory-detail-wrapper': true, 'full-height': !inventoryId }">
  <header *ngIf="!inventoryId" class="sticky-top">
    <div class="logo">
      <div class="dealer-name">
        <em class="pi pi-chevron-left pointer back-button me-2 print-hide" (click)="onCancel()" [pTooltip]="'Go Back'"></em>
      </div>
    </div>
  </header>
  <div class="inventory-wrapper">
    <div class="row mx-0" *ngIf="!isLoading; else showLoader">
      <ng-container *ngIf="!noDealerExist; else noSuchDealerExist">
        <div class="col-lg-12 col-xl-5 px-0 inventory-images">
          <div class="img-wrapper row px-3 mt-1">
            <ng-container *ngIf="imagesToShow?.length; else noImageToDisplay">
              <div *ngFor="let image of imagesToShow; let index = index" class="image-content" [ngClass]="{'col-12': image.displayPicture, 'col-xl-3 col-lg-2 col-md-3 col-sm-4 col-6': !image.displayPicture}">
                <img [src]="image.fullUrl" alt="" (click)="openCarousel(index)" />
              </div>
            </ng-container>
            <ng-template #noImageToDisplay>
              <img class="no-image-found" [src]="constants.staticImages.noImages" alt="" />
            </ng-template>
          </div>
        </div>
        <div class="cd-lg-12 col-xl-7 inventory-details">
          <div class="d-flex justify-content-end align-items-center w-100 action-btns" *ngIf="!inventoryId">
            <button type="button" class="btn public-page-action-btn print-hide px-3 action-btn" (click)="copyToClipboard()" pTooltip="Share" tooltipPosition="top">
              <em class="pi pi-share-alt"></em> <span class="text-span ms-2">Share</span>
            </button>
            <button type="button" class="btn public-page-action-btn ms-3 print-hide px-3 action-btn" (click)="print()" pTooltip="Print" tooltipPosition="top">
              <em class="pi pi-print"></em> <span class="text-span ms-2">Print</span>
            </button>
            <button type="button" class="btn public-page-action-btn ms-3 print-hide px-3 action-btn" pTooltip="Get A Quote" tooltipPosition="top" (click)="showQuoteDialog()">
              <span class="img-span"><img [src]="constants.staticImages.icons.GetQuote" alt="getAQuote-icon" /></span><span class="text-span ms-1">Get A Quote</span>
            </button>
            <button type="button" class="btn public-page-action-btn print-hide mx-3 px-3 action-btn" (click)="onDownloadUploadedDocuments()" pTooltip="Document" tooltipPosition="top">
              <em class="pi pi-download"></em> <span class="text-span ms-2">Documents</span>
            </button>
          </div>
          <div *ngFor="let publicInventoryDetails of publicInventoryList" class="card p-3 m-2">
            <p-accordion class="w-100 remove-border" [multiple]="true">
              <p-accordionTab [(selected)]="publicInventoryDetails.toggleTab">
                <ng-template pTemplate="header">
                  <div class="accordion-header">
                    <div class="basic-details d-sm-flex align-items-center">
                      <div class="model-name">
                        {{ publicInventoryDetails?.generalInformation?.year }}
                        {{ publicInventoryDetails?.generalInformation?.make?.name }}
                        {{ publicInventoryDetails?.generalInformation?.unitModel?.name }}
                      </div>
                    </div>
                    <em class="pi print-hide" [ngClass]="publicInventoryDetails.toggleTab ? 'pi-minus' : 'pi-plus'"></em>
                  </div>
                </ng-template>
                <ng-template pTemplate="content">
                  <div class="basic-details d-sm-flex align-items-center">
                    <div>
                      <div class="model-description">
                        {{ publicInventoryDetails?.generalInformation?.make?.name }}
                        {{ publicInventoryDetails?.generalInformation?.unitModel?.name }}
                      </div>
                      <div class="model-conditional-details">
                        <div class="miles">
                          <img [src]="constants.staticImages.icons.truckBlack" alt="" />
                          <span>
                            {{ publicInventoryDetails?.odometer?.odometerReading ? (publicInventoryDetails?.odometer?.odometerReading | number: '1.0-0') : 0 }}
                            {{ publicInventoryDetails?.odometer?.unitOfDistance?.name }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="retail-price">
                    <ng-container
                      [ngTemplateOutlet]="modelDetails"
                      [ngTemplateOutletContext]="{
                        generalInfo: publicInventoryDetails?.retailAskingPrice
                          ? (publicInventoryDetails?.retailAskingPrice | currency: 'USD':true:'1.0-0')
                          : 'Please call for price details',
                        label: 'Retail Price '
                      }"
                    >
                    </ng-container>
                  </div>
                  <div class="general-information-wrapper">
                    <div class="information-title">GENERAL INFORMATION</div>
                    <div class="general-information">
                      <div class="info-label">
                        <div class="model-detail-label">Stock#</div>
                        <div class="model-detail-label">VIN#</div>
                        <div class="model-detail-label">Year</div>
                        <div class="model-detail-label">Make</div>
                        <div class="model-detail-label">Model</div>
                        <div class="model-detail-label">Unit Type</div>
                        <div class="model-detail-label">Owned By</div>
                      </div>
                      <div class="info-value">
                        <div class="model-detail-info">
                          {{ publicInventoryDetails?.generalInformation?.stockNumber }}
                        </div>
                        <div class="model-detail-info">
                          <span>{{ publicInventoryDetails?.generalInformation?.vin ? publicInventoryDetails?.generalInformation?.vin : '-' }}</span>
                        </div>
                        <div class="model-detail-info">
                          {{ publicInventoryDetails?.generalInformation?.year }}
                        </div>
                        <div class="model-detail-info">
                          {{ publicInventoryDetails?.generalInformation?.make?.name }}
                        </div>
                        <div class="model-detail-info">
                          {{ publicInventoryDetails?.generalInformation?.unitModel?.name }}
                        </div>
                        <div class="model-detail-info">
                          {{ publicInventoryDetails?.generalInformation?.unitType?.name }}
                        </div>
                        <div class="model-detail-info">
                          {{ publicInventoryDetails?.generalInformation?.owner?.name }}
                        </div>
                      </div>
                    </div>
                    <div class="specification-information-wrapper">
                      <p-accordion class="w-100" [multiple]="true" *ngIf="publicInventoryDetails.accordionTabs">
                        <p-accordionTab [(selected)]="publicInventoryDetails.accordionTabs.contactInfo">
                          <ng-template pTemplate="header">
                            <div class="accordion-header">
                              <span>DEALERSHIP CONTACT INFORMATION</span>
                              <em class="pi print-hide" [ngClass]="publicInventoryDetails.accordionTabs.contactInfo ? 'pi-minus' : 'pi-plus'"></em>
                            </div>
                          </ng-template>
                          <ng-template pTemplate="content">
                            <div class="general-information">
                              <div class="info-label">
                                <div class="model-detail-label">Name</div>
                                <div class="model-detail-label">Phone Number</div>
                                <div class="model-detail-label">Address</div>
                              </div>
                              <div class="info-value">
                                <div class="model-detail-info">
                                  {{ publicInventoryDetails?.delear?.contactPerson?.firstName }} {{ publicInventoryDetails?.delear?.contactPerson?.lastName }}
                                </div>
                                <div class="model-detail-info">
                                  {{ publicInventoryDetails?.delear?.contactPerson?.phoneNumber | phone }}
                                </div>
                                <div class="model-detail-info">
                                  {{ publicInventoryDetails?.delear?.address?.streetAddress }}, {{ publicInventoryDetails?.delear?.address?.city }},
                                  {{ publicInventoryDetails?.delear?.address?.state }},
                                  {{ publicInventoryDetails?.delear?.address?.zipcode }}
                                </div>
                              </div>
                            </div>
                          </ng-template>
                        </p-accordionTab>
                        <p-accordionTab [(selected)]="publicInventoryDetails.accordionTabs.specification">
                          <ng-template pTemplate="header">
                            <div class="accordion-header">
                              <span>SPECIFICATION INFORMATION</span>
                              <em class="pi print-hide" [ngClass]="publicInventoryDetails.accordionTabs.specification ? 'pi-minus' : 'pi-plus'"></em>
                            </div>
                          </ng-template>
                          <ng-template pTemplate="content">
                            <ng-container *ngIf="isSpecificationDataPresent(publicInventoryDetails?.specifications?.specificationData?.specification); else noSpecificationFound">
                              <div class="section mt-2" *ngFor="let specification of publicInventoryDetails?.specifications?.specificationData?.specification">
                                <div class="title" *ngIf="showSpecificationGroupName(specification)">
                                  {{ specification.sectionName }}
                                </div>
                                <div class="specification-information">
                                  <div class="info-label">
                                    <ng-container *ngFor="let specificationLable of specification.fields">
                                      <div class="model-detail-label" *ngIf="specificationLable.value">
                                        {{ specificationLable.for }}
                                      </div>
                                    </ng-container>
                                  </div>
                                  <div class="info-value">
                                    <ng-container *ngFor="let specificationLable of specification.fields">
                                      <div class="model-detail-info" *ngIf="specificationLable.value">
                                        <ng-container *ngIf="specificationLable.dataType !== 'DropDown'; else dropdown">
                                          {{ specificationLable.value ? specificationLable.value : '-' }}
                                        </ng-container>
                                        <ng-template #dropdown>
                                          {{ getSelectedOptionName(specificationLable) }}
                                        </ng-template>
                                      </div>
                                    </ng-container>
                                  </div>
                                </div>
                              </div>
                            </ng-container>
                            <ng-template #noSpecificationFound>
                              <div class="section text-center">No Specification Available</div>
                            </ng-template>
                          </ng-template>
                        </p-accordionTab>
                        <p-accordionTab [(selected)]="publicInventoryDetails.accordionTabs.notes">
                          <ng-template pTemplate="header">
                            <div class="accordion-header">
                              <span>NOTES</span>
                              <em class="pi print-hide" [ngClass]="publicInventoryDetails.accordionTabs.notes ? 'pi-minus' : 'pi-plus'"></em>
                            </div>
                          </ng-template>
                          <ng-template pTemplate="content">
                            <ng-container *ngIf="publicInventoryDetails?.notes?.length; else noNoteFound">
                              <div class="section mt-2" *ngFor="let note of publicInventoryDetails?.notes">
                                <div class="title">
                                  {{ note.noteType }}
                                </div>
                                <div>
                                  <p-editor #editor [(ngModel)]="note.note" [readonly]="true">
                                    <p-header> </p-header>
                                  </p-editor>
                                </div>
                              </div>
                            </ng-container>
                            <ng-template #noNoteFound>
                              <div class="section text-center">No Note Available</div>
                            </ng-template>
                          </ng-template>
                        </p-accordionTab>
                        <p-accordionTab [(selected)]="publicInventoryDetails.accordionTabs.documents">
                          <ng-template pTemplate="header">
                            <div class="accordion-header">
                              <span>DOCUMENTS</span>
                              <em class="pi print-hide" [ngClass]="publicInventoryDetails.accordionTabs.documents ? 'pi-minus' : 'pi-plus'"></em>
                            </div>
                          </ng-template>
                          <ng-template pTemplate="content">
                            <ng-container *ngIf="publicInventoryDetails?.documents?.length; else noDocumentFound">
                              <div class="section mt-2">
                                <div class="document-information" *ngFor="let document of publicInventoryDetails?.documents">
                                  <img [src]="constants.staticImages.icons.pdfIcon" alt="pdf-icon" />
                                  <div class="info-value">
                                    {{ document?.fileName }}
                                  </div>
                                  <div class="ms-auto me-3">
                                    <ng-container>
                                      <a [href]="document?.documentUrl" target="_blank">
                                        <img [src]="constants.staticImages.icons.viewIcon" alt="" class="view-icon print-hide" />
                                      </a>
                                      <a (click)="downloadServerPDF(document)">
                                        <img [src]="constants.staticImages.icons.download" alt="" class="view-icon print-hide" />
                                      </a>
                                    </ng-container>
                                  </div>
                                </div>
                              </div>
                            </ng-container>
                            <ng-template #noDocumentFound>
                              <div class="section mt-2 text-center">No Document Available</div>
                            </ng-template>
                          </ng-template>
                        </p-accordionTab>
                      </p-accordion>
                    </div>
                  </div>
                </ng-template>
              </p-accordionTab>
            </p-accordion>
          </div>
        </div>
      </ng-container>
      <ng-template #noSuchDealerExist>
        <div class="text-center mt-4">This Inventory does not belong to selected dealer.</div>
      </ng-template>
    </div>
  </div>
  <app-footer *ngIf="!inventoryId" class="sticky-bottom"></app-footer>
</div>

<ng-template #modelDetails let-generalInfo="generalInfo" let-label="label">
  <span class="model-detail-label">
    {{ label }}
  </span>
  <span class="model-detail-info">
    {{ generalInfo ? generalInfo : '-' }}
  </span>
</ng-template>

<app-image-zoom-overlay
  [images]="zoomImages"
  [selectedIndex]="selectedPhotoIndex"
  [isVisible]="isShowCarousel"
  (close)="closeZoomOverlay()"
  (indexChange)="onImageIndexChange($event)">
</app-image-zoom-overlay>

<p-dialog
  [(visible)]="isShowQuoteDialog"
  *ngIf="isShowQuoteDialog"
  [modal]="true"
  [breakpoints]="{ '1440px': '60vw', '1024px': '75vw', '640px': '90vw' }"
  [style]="{ width: '40vw' }"
>
  <ng-template pTemplate="header"> Get A Quote For {{ publicInventoryList[0]?.generalInformation?.stockNumber }} </ng-template>
  <p-card class="px-2 mt-2">
    <app-quote-form (onClose)="closeQuoteDialog()" [stockNumber]="publicInventoryList[0]?.generalInformation?.stockNumber ?? ''"></app-quote-form>
  </p-card>
</p-dialog>

<ng-template #showLoader>
  <div class="listing-loader" style="scale: 3">
    <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
  </div>
</ng-template>
