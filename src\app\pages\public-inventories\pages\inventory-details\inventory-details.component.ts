import { Clipboard } from '@angular/cdk/clipboard';
import { ChangeDetectorRef, Component, ElementRef, EventEmitter, HostListener, Input, OnInit, Output, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent, ROUTER_UTILS } from '@core/utils';
import { AuthService } from '@pages/auth/services/auth.service';
import { AddInventorySpecificationParams, InventorySpecification, InventorySpecificationFields } from '@pages/inventory/models';
import { DocumentListItem } from '@pages/inventory/models/documents.model';
import { NotesDetailsResponse } from '@pages/inventory/models/notes.model';
import { DealerDetails, PublicImages, PublicInventory } from '@pages/public-inventories/models';
import { InventoriesService } from '@pages/public-inventories/services';
import * as saveAs from 'file-saver';
import { Observable, distinctUntilChanged, forkJoin, takeUntil } from 'rxjs';
import { FileUploadService } from 'src/app/@shared/services/file-upload.service';
import { THEMES, ThemeService } from 'src/app/@shared/services/theme.service';

@Component({
  selector: 'app-inventory-details',
  templateUrl: './inventory-details.component.html',
  styleUrls: ['./inventory-details.component.scss']
})
export class InventoryDetailsComponent extends BaseComponent implements OnInit {
  @ViewChild('zoomImage', { static: false }) zoomImageRef!: ElementRef<HTMLImageElement>;
  @ViewChild('imageContainer', { static: false }) imageContainerRef!: ElementRef<HTMLDivElement>;

  @Input() inventoryId!: number;
  @Input() dealerName!: string;
  @Output() onClose = new EventEmitter<boolean>();

  // Original properties
  imagesToShow!: Array<PublicImages>;
  isLoggedIn$!: Observable<boolean>;
  publicInventoryList: PublicInventory[] = [];
  selectedPhotoIndex!: number;
  noDealerExist = false;
  isShowCarousel = false;
  isDarkMode = false;
  isShowQuoteDialog: boolean = false;
  primaryInventoryId!: number;
  accordionStates: boolean[] = [];
  isLoading = false;
  inventoryIds: number[] = [];
  accordionTabs = {
    contactInfo: true,
    specification: true,
    notes: true,
    documents: true
  };

  // Advanced Zoom Properties
  zoomLevel: number = 1;
  minZoom: number = 0.5;
  maxZoom: number = 5;
  panX: number = 0;
  panY: number = 0;
  isPanning: boolean = false;
  lastPanX: number = 0;
  lastPanY: number = 0;
  imageLoading: boolean = false;
  showInstructions: boolean = true;

  // Magnifier Properties
  magnifierEnabled: boolean = false;
  magnifierSize: number = 200;
  magnifierZoom: number = 3;
  magnifierPosition = { x: 0, y: 0 };
  showMagnifier: boolean = false;

  // Math reference for template
  Math = Math;

  constructor(
    private readonly inventoriesService: InventoriesService,
    private readonly fileUploadService: FileUploadService,
    private readonly authService: AuthService,
    private readonly cdf: ChangeDetectorRef,
    private readonly themeService: ThemeService,
    private readonly router: Router,
    private readonly activeRoute: ActivatedRoute,
    private readonly clipBoard: Clipboard,
    private readonly toasterService: AppToasterService,
  ) {
    super();
  }

  ngOnInit(): void {
    this.isLoggedIn$ = this.authService.isLoggedIn$;
    this.subscribeToTheme();
    if (this.inventoryId && this.dealerName) {
      this.getInventory(this.inventoryId, this.dealerName);
    } else {
      this.activeRoute.queryParams.subscribe(params => {
        if (params?.id && params?.abbreviation) {
          this.dealerName = params.abbreviation;
          this.primaryInventoryId = Number(params?.id);
          this.getInventory(Number(params?.id), params?.abbreviation ?? '');
        }
      });
    }
  }

  ngAfterViewInit() {
    // Hide instructions after 5 seconds
    setTimeout(() => {
      this.showInstructions = false;
      this.cdf.detectChanges();
    }, 5000);
  }

  // Keyboard navigation
  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    if (!this.isShowCarousel) return;

    switch (event.key) {
      case 'Escape':
        this.closeZoomOverlay();
        break;
      case 'ArrowLeft':
        this.navigateImage('prev');
        break;
      case 'ArrowRight':
        this.navigateImage('next');
        break;
      case '+':
      case '=':
        this.zoomIn();
        break;
      case '-':
        this.zoomOut();
        break;
      case '0':
        this.resetZoom();
        break;
    }
  }

  // Advanced Zoom Methods
  openCarousel(selectedPhotoIndex: number): void {
    this.selectedPhotoIndex = selectedPhotoIndex;
    this.isShowCarousel = true;
    this.resetZoomState();
    document.body.style.overflow = 'hidden';
  }

  closeZoomOverlay(): void {
    this.isShowCarousel = false;
    this.resetZoomState();
    document.body.style.overflow = 'auto';
  }

  resetZoomState(): void {
    this.zoomLevel = 1;
    this.panX = 0;
    this.panY = 0;
    this.isPanning = false;
    this.magnifierEnabled = false;
    this.showMagnifier = false;
    this.imageLoading = true;
  }

  getDealerDetails(dealerName?: string): void {
    this.inventoriesService.get<DealerDetails>(`${API_URL_UTIL.subDomain.dealer}/${API_URL_UTIL.subDomain.details}/${API_URL_UTIL.subDomain.abbreviation}/${dealerName}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: DealerDetails) => {
          this.publicInventoryList = this.publicInventoryList.map(inventory => {
            return (res.abbreviation === inventory.generalInformation.owner.abbreviation) ? { ...inventory, delear: res } : { ...inventory }
          })
          const publicInventory = this.publicInventoryList.find(publicInventory => publicInventory.id === this.primaryInventoryId)
          this.noDealerExist = publicInventory && !res ? true : false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.noDealerExist = true;
        }
      });
  }

  getInventory(id: number, abbreviation: string): void {
    this.isLoading = true;
    this.inventoriesService.get<PublicInventory>(`${API_URL_UTIL.inventory.unitDetails}/${id}?abbreviation=${abbreviation}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: PublicInventory) => {
          this.publicInventoryList.push(res);
          this.getDealerDetails(abbreviation);
          this.getInventoryPhotos(id);
          this.inventoryIds.push(id)
          if (res.unitAssociations.length) {
            res.unitAssociations.forEach(unitAssociation => {
              const unitIds = res.unitAssociations.map(associate => associate.id)
              this.inventoryIds.push(...unitIds)
              this.getAssociatedInventory(unitAssociation.id, unitAssociation.owner.abbreviation, this.inventoryIds);
            })
          }
          this.commonAPICall(this.inventoryIds);
          setTimeout(() => {
            this.isLoading = false;
            this.cdf.detectChanges();
          }, 1000)
        }
      });
  }

  commonAPICall(ids: number[]): void {
    forkJoin({
      specification: this.getInventorySpecification(ids),
      notes: this.getInventoryNotes(ids),
      documents: this.getInventoryDocuments(ids)
    }).subscribe({
      next: (res) => {
        this.publicInventoryList = this.publicInventoryList.map(inventory => {
          const specification = res.specification.find(specification => specification.unitId === inventory.id)
          const notes = res.notes.filter(note => note.unitId === inventory.id)
          const documents = res.documents.filter(doc => doc.unitId === inventory.id)
          return {
            ...inventory,
            specifications: specification ? specification : {} as AddInventorySpecificationParams,
            notes: notes ? notes : [],
            documents: documents ? documents : [],
            accordionTabs: { ...this.accordionTabs }, toggleTab: true
          }
        })
      }
    });
  }


  getAssociatedInventory(id: number, abbreviation: string, ids: number[]): void {
    this.inventoriesService.get<PublicInventory>(`${API_URL_UTIL.inventory.unitDetails}/${id}?abbreviation=${abbreviation}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: PublicInventory) => {
          this.publicInventoryList.push(res);
          this.getDealerDetails(abbreviation);
          this.cdf.detectChanges();
        }
      });
  }

  getInventoryPhotos(id: number): void {
    this.inventoriesService.get<Array<PublicImages>>(id, `${API_URL_UTIL.inventory.unitImages}/${API_URL_UTIL.inventory.unit}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: Array<PublicImages>) => {
          this.imagesToShow = res;
          this.sortImages();
          this.cdf.detectChanges();
        }
      });
  }

  sortImages() {
    this.imagesToShow.sort((a, b) => {
      if (a.displayPicture && !b.displayPicture) {
        return -1;
      } else if (!a.displayPicture && b.displayPicture) {
        return 1;
      } else {
        // TODO Will be remove once complete
        // return 0;
        return a.imageOrder - b.imageOrder;
      }
    });
  }

  getInventorySpecification(ids: number[]): Observable<AddInventorySpecificationParams[]> {
    return this.inventoriesService.getSpecifications(ids).pipe(takeUntil(this.destroy$));
  }

  getInventoryNotes(ids: number[]): Observable<NotesDetailsResponse[]> {
    return this.inventoriesService.getNotes(ids).pipe(takeUntil(this.destroy$));
  }

  getInventoryDocuments(ids: number[]): Observable<DocumentListItem[]> {
    return this.inventoriesService.getDocuments(ids).pipe(takeUntil(this.destroy$));
  }

  downloadServerPDF(file: DocumentListItem | null): void {
    if (file?.url) {
      this.fileUploadService.downloadFile(file.url, file.fileName).pipe(takeUntil(this.destroy$)).subscribe({
        next: (response: ArrayBuffer) => {
          saveAs(new Blob([response], { type: "application/pdf" }), file.fileName);
        }
      });
    }
  }

  // Navigation Methods
  navigateImage(direction: 'prev' | 'next'): void {
    if (direction === 'prev' && this.selectedPhotoIndex > 0) {
      this.selectedPhotoIndex--;
    } else if (direction === 'next' && this.selectedPhotoIndex < this.imagesToShow.length - 1) {
      this.selectedPhotoIndex++;
    }
    this.resetZoomState();
  }

  getCurrentImage(): PublicImages | undefined {
    return this.imagesToShow[this.selectedPhotoIndex];
  }

  // Zoom Control Methods
  zoomIn(): void {
    if (this.zoomLevel < this.maxZoom) {
      this.zoomLevel = Math.min(this.zoomLevel * 1.2, this.maxZoom);
    }
  }

  zoomOut(): void {
    if (this.zoomLevel > this.minZoom) {
      this.zoomLevel = Math.max(this.zoomLevel / 1.2, this.minZoom);
      // Reset pan if zoomed out too much
      if (this.zoomLevel <= 1) {
        this.panX = 0;
        this.panY = 0;
      }
    }
  }

  resetZoom(): void {
    this.zoomLevel = 1;
    this.panX = 0;
    this.panY = 0;
  }

  // Mouse wheel zoom
  onWheel(event: WheelEvent): void {
    event.preventDefault();
    const delta = event.deltaY > 0 ? -1 : 1;
    const zoomFactor = 1.1;

    if (delta > 0 && this.zoomLevel < this.maxZoom) {
      this.zoomLevel = Math.min(this.zoomLevel * zoomFactor, this.maxZoom);
    } else if (delta < 0 && this.zoomLevel > this.minZoom) {
      this.zoomLevel = Math.max(this.zoomLevel / zoomFactor, this.minZoom);
      if (this.zoomLevel <= 1) {
        this.panX = 0;
        this.panY = 0;
      }
    }
  }

  // Pan Methods
  startPan(event: MouseEvent): void {
    if (this.zoomLevel <= 1) return;

    this.isPanning = true;
    this.lastPanX = event.clientX;
    this.lastPanY = event.clientY;
    event.preventDefault();
  }

  onPan(event: MouseEvent): void {
    if (this.magnifierEnabled && !this.isPanning) {
      this.updateMagnifierPosition(event);
      return;
    }

    if (!this.isPanning || this.zoomLevel <= 1) return;

    const deltaX = event.clientX - this.lastPanX;
    const deltaY = event.clientY - this.lastPanY;

    this.panX += deltaX;
    this.panY += deltaY;

    this.lastPanX = event.clientX;
    this.lastPanY = event.clientY;
  }

  endPan(event: MouseEvent): void {
    this.isPanning = false;
    this.showMagnifier = false;
  }

  // Magnifier Methods
  toggleMagnifier(): void {
    this.magnifierEnabled = !this.magnifierEnabled;
    this.showMagnifier = false;
  }

  updateMagnifierPosition(event: MouseEvent): void {
    if (!this.magnifierEnabled) return;

    const container = this.imageContainerRef?.nativeElement;
    if (!container) return;

    const rect = container.getBoundingClientRect();
    this.magnifierPosition.x = event.clientX - rect.left;
    this.magnifierPosition.y = event.clientY - rect.top;
    this.showMagnifier = true;
  }

  // Transform and Style Methods
  getImageTransform(): string {
    return `scale(${this.zoomLevel}) translate(${this.panX / this.zoomLevel}px, ${this.panY / this.zoomLevel}px)`;
  }

  getCursor(): string {
    if (this.magnifierEnabled) return 'crosshair';
    if (this.zoomLevel > 1) return this.isPanning ? 'grabbing' : 'grab';
    return 'default';
  }

  onImageLoad(): void {
    this.imageLoading = false;
  }

  getMagnifierBackgroundSize(): string {
    const scale = this.zoomLevel * this.magnifierZoom;
    return `${scale * 100}%`;
  }

  getMagnifierBackgroundPosition(): string {
    const image = this.zoomImageRef?.nativeElement;
    const container = this.imageContainerRef?.nativeElement;

    if (!image || !container) return '0% 0%';

    const imageRect = image.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();

    const relativeX = this.magnifierPosition.x - (imageRect.left - containerRect.left);
    const relativeY = this.magnifierPosition.y - (imageRect.top - containerRect.top);

    const percentX = (relativeX / imageRect.width) * 100;
    const percentY = (relativeY / imageRect.height) * 100;

    return `${percentX}% ${percentY}%`;
  }

  getSelectedOptionName(field: InventorySpecificationFields): string {
    if (field.value && field.options.length) {
      const name = field.options.find(o => o.id === Number(field.value))?.name
      return name ?? '-'
    }
    return '-'
  }

  showSpecificationGroupName(specification: InventorySpecification): boolean {
    return specification.fields.some(field => field.value);
  }

  isSpecificationDataPresent(specifications: InventorySpecification[] | undefined): boolean {
    let isSpecificationDataPresent = false
    if (specifications?.length) {
      for (const specification of specifications) {
        if (!isSpecificationDataPresent) {
          isSpecificationDataPresent = specification.fields.some(field => field.value)
        }
      }
    }
    return isSpecificationDataPresent;
  }

  subscribeToTheme(): void {
    this.themeService.selectedTheme$
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((theme: THEMES) => {
        if (theme === THEMES.DARK) {
          this.isDarkMode = true;
        } else {
          this.isDarkMode = false;
        }
        this.cdf.detectChanges();
      });
  }

  showQuoteDialog() {
    this.isShowQuoteDialog = true;
  }

  closeQuoteDialog() {
    this.isShowQuoteDialog = false;
  }

  goBack(): void {
    this.router.navigateByUrl(`/${ROUTER_UTILS.config.publicInvetory.root}/${this.dealerName}`);
  }

  onCancel(): void {
    this.onClose.emit(false);
    this.goBack()
  }

  copyToClipboard(): void {
    const isCopied = this.clipBoard.copy(window.location.href);
    isCopied ? this.toasterService.success(MESSAGES.pageUrlCopiedSuccess) : this.toasterService.error(MESSAGES.pageUrlCopiedFailed);
  }

  print(): void {
    window.print()
  }

  onDownloadUploadedDocuments(): void {
    this.publicInventoryList.forEach(publicInventory => {
      if (publicInventory?.documents?.length) {
        for (const documents of publicInventory.documents) {
          this.downloadServerPDF(documents);
        }
      } else {
        this.toasterService.info(MESSAGES.noDocumentsAvailable);
      }
    })
  }
}
