import { Clipboard } from '@angular/cdk/clipboard';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent, ROUTER_UTILS } from '@core/utils';
import { AuthService } from '@pages/auth/services/auth.service';
import { AddInventorySpecificationParams, InventorySpecification, InventorySpecificationFields } from '@pages/inventory/models';
import { DocumentListItem } from '@pages/inventory/models/documents.model';
import { NotesDetailsResponse } from '@pages/inventory/models/notes.model';
import { DealerDetails, PublicImages, PublicInventory } from '@pages/public-inventories/models';
import { InventoriesService } from '@pages/public-inventories/services';
import * as saveAs from 'file-saver';
import { Observable, distinctUntilChanged, forkJoin, takeUntil } from 'rxjs';
import { FileUploadService } from 'src/app/@shared/services/file-upload.service';
import { THEMES, ThemeService } from 'src/app/@shared/services/theme.service';
import { ZoomImage } from 'src/app/shared/components/image-zoom-overlay/image-zoom-overlay.component';

@Component({
  selector: 'app-inventory-details',
  templateUrl: './inventory-details.component.html',
  styleUrls: ['./inventory-details.component.scss']
})
export class InventoryDetailsComponent extends BaseComponent implements OnInit {

  @Input() inventoryId!: number;
  @Input() dealerName!: string;
  @Output() onClose = new EventEmitter<boolean>();

  // Original properties
  imagesToShow!: Array<PublicImages>;
  isLoggedIn$!: Observable<boolean>;
  publicInventoryList: PublicInventory[] = [];
  selectedPhotoIndex!: number;
  noDealerExist = false;
  isShowCarousel = false;

  // Zoom Images for reusable component
  zoomImages: ZoomImage[] = [];
  isDarkMode = false;
  isShowQuoteDialog: boolean = false;
  primaryInventoryId!: number;
  accordionStates: boolean[] = [];
  isLoading = false;
  inventoryIds: number[] = [];
  accordionTabs = {
    contactInfo: true,
    specification: true,
    notes: true,
    documents: true
  };



  constructor(
    private readonly inventoriesService: InventoriesService,
    private readonly fileUploadService: FileUploadService,
    private readonly authService: AuthService,
    private readonly cdf: ChangeDetectorRef,
    private readonly themeService: ThemeService,
    private readonly router: Router,
    private readonly activeRoute: ActivatedRoute,
    private readonly clipBoard: Clipboard,
    private readonly toasterService: AppToasterService,
  ) {
    super();
  }

  ngOnInit(): void {
    this.isLoggedIn$ = this.authService.isLoggedIn$;
    this.subscribeToTheme();
    if (this.inventoryId && this.dealerName) {
      this.getInventory(this.inventoryId, this.dealerName);
    } else {
      this.activeRoute.queryParams.subscribe(params => {
        if (params?.id && params?.abbreviation) {
          this.dealerName = params.abbreviation;
          this.primaryInventoryId = Number(params?.id);
          this.getInventory(Number(params?.id), params?.abbreviation ?? '');
        }
      });
    }
  }

  ngAfterViewInit() {
    // Component initialization
  }

  // Keyboard navigation


  // Advanced Zoom Methods
  openCarousel(selectedPhotoIndex: number): void {
    this.selectedPhotoIndex = selectedPhotoIndex;
    this.isShowCarousel = true;
    document.body.style.overflow = 'hidden';
  }

  closeZoomOverlay(): void {
    this.isShowCarousel = false;
    document.body.style.overflow = 'auto';
  }

  onImageIndexChange(newIndex: number): void {
    this.selectedPhotoIndex = newIndex;
  }



  getDealerDetails(dealerName?: string): void {
    this.inventoriesService.get<DealerDetails>(`${API_URL_UTIL.subDomain.dealer}/${API_URL_UTIL.subDomain.details}/${API_URL_UTIL.subDomain.abbreviation}/${dealerName}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: DealerDetails) => {
          this.publicInventoryList = this.publicInventoryList.map(inventory => {
            return (res.abbreviation === inventory.generalInformation.owner.abbreviation) ? { ...inventory, delear: res } : { ...inventory }
          })
          const publicInventory = this.publicInventoryList.find(publicInventory => publicInventory.id === this.primaryInventoryId)
          this.noDealerExist = publicInventory && !res ? true : false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.noDealerExist = true;
        }
      });
  }

  getInventory(id: number, abbreviation: string): void {
    this.isLoading = true;
    this.inventoriesService.get<PublicInventory>(`${API_URL_UTIL.inventory.unitDetails}/${id}?abbreviation=${abbreviation}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: PublicInventory) => {
          this.publicInventoryList.push(res);
          this.getDealerDetails(abbreviation);
          this.getInventoryPhotos(id);
          this.inventoryIds.push(id)
          if (res.unitAssociations.length) {
            res.unitAssociations.forEach(unitAssociation => {
              const unitIds = res.unitAssociations.map(associate => associate.id)
              this.inventoryIds.push(...unitIds)
              this.getAssociatedInventory(unitAssociation.id, unitAssociation.owner.abbreviation, this.inventoryIds);
            })
          }
          this.commonAPICall(this.inventoryIds);
          setTimeout(() => {
            this.isLoading = false;
            this.cdf.detectChanges();
          }, 1000)
        }
      });
  }

  commonAPICall(ids: number[]): void {
    forkJoin({
      specification: this.getInventorySpecification(ids),
      notes: this.getInventoryNotes(ids),
      documents: this.getInventoryDocuments(ids)
    }).subscribe({
      next: (res) => {
        this.publicInventoryList = this.publicInventoryList.map(inventory => {
          const specification = res.specification.find(specification => specification.unitId === inventory.id)
          const notes = res.notes.filter(note => note.unitId === inventory.id)
          const documents = res.documents.filter(doc => doc.unitId === inventory.id)
          return {
            ...inventory,
            specifications: specification ? specification : {} as AddInventorySpecificationParams,
            notes: notes ? notes : [],
            documents: documents ? documents : [],
            accordionTabs: { ...this.accordionTabs }, toggleTab: true
          }
        })
      }
    });
  }


  getAssociatedInventory(id: number, abbreviation: string, ids: number[]): void {
    this.inventoriesService.get<PublicInventory>(`${API_URL_UTIL.inventory.unitDetails}/${id}?abbreviation=${abbreviation}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: PublicInventory) => {
          this.publicInventoryList.push(res);
          this.getDealerDetails(abbreviation);
          this.cdf.detectChanges();
        }
      });
  }

  getInventoryPhotos(id: number): void {
    this.inventoriesService.get<Array<PublicImages>>(id, `${API_URL_UTIL.inventory.unitImages}/${API_URL_UTIL.inventory.unit}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: Array<PublicImages>) => {
          this.imagesToShow = res;
          this.sortImages();
          // Populate zoom images for reusable component
          this.zoomImages = this.imagesToShow.map(image => ({
            fullUrl: image.fullUrl,
            alt: `Inventory Image ${image.id}`
          }));
          this.cdf.detectChanges();
        }
      });
  }

  sortImages() {
    this.imagesToShow.sort((a, b) => {
      if (a.displayPicture && !b.displayPicture) {
        return -1;
      } else if (!a.displayPicture && b.displayPicture) {
        return 1;
      } else {
        // TODO Will be remove once complete
        // return 0;
        return a.imageOrder - b.imageOrder;
      }
    });
  }

  getInventorySpecification(ids: number[]): Observable<AddInventorySpecificationParams[]> {
    return this.inventoriesService.getSpecifications(ids).pipe(takeUntil(this.destroy$));
  }

  getInventoryNotes(ids: number[]): Observable<NotesDetailsResponse[]> {
    return this.inventoriesService.getNotes(ids).pipe(takeUntil(this.destroy$));
  }

  getInventoryDocuments(ids: number[]): Observable<DocumentListItem[]> {
    return this.inventoriesService.getDocuments(ids).pipe(takeUntil(this.destroy$));
  }

  downloadServerPDF(file: DocumentListItem | null): void {
    if (file?.url) {
      this.fileUploadService.downloadFile(file.url, file.fileName).pipe(takeUntil(this.destroy$)).subscribe({
        next: (response: ArrayBuffer) => {
          saveAs(new Blob([response], { type: "application/pdf" }), file.fileName);
        }
      });
    }
  }



  getCurrentImage(): PublicImages | undefined {
    return this.imagesToShow[this.selectedPhotoIndex];
  }







  getSelectedOptionName(field: InventorySpecificationFields): string {
    if (field.value && field.options.length) {
      const name = field.options.find(o => o.id === Number(field.value))?.name
      return name ?? '-'
    }
    return '-'
  }

  showSpecificationGroupName(specification: InventorySpecification): boolean {
    return specification.fields.some(field => field.value);
  }

  isSpecificationDataPresent(specifications: InventorySpecification[] | undefined): boolean {
    let isSpecificationDataPresent = false
    if (specifications?.length) {
      for (const specification of specifications) {
        if (!isSpecificationDataPresent) {
          isSpecificationDataPresent = specification.fields.some(field => field.value)
        }
      }
    }
    return isSpecificationDataPresent;
  }

  subscribeToTheme(): void {
    this.themeService.selectedTheme$
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((theme: THEMES) => {
        if (theme === THEMES.DARK) {
          this.isDarkMode = true;
        } else {
          this.isDarkMode = false;
        }
        this.cdf.detectChanges();
      });
  }

  showQuoteDialog() {
    this.isShowQuoteDialog = true;
  }

  closeQuoteDialog() {
    this.isShowQuoteDialog = false;
  }

  goBack(): void {
    this.router.navigateByUrl(`/${ROUTER_UTILS.config.publicInvetory.root}/${this.dealerName}`);
  }

  onCancel(): void {
    this.onClose.emit(false);
    this.goBack()
  }

  copyToClipboard(): void {
    const isCopied = this.clipBoard.copy(window.location.href);
    isCopied ? this.toasterService.success(MESSAGES.pageUrlCopiedSuccess) : this.toasterService.error(MESSAGES.pageUrlCopiedFailed);
  }

  print(): void {
    window.print()
  }

  onDownloadUploadedDocuments(): void {
    this.publicInventoryList.forEach(publicInventory => {
      if (publicInventory?.documents?.length) {
        for (const documents of publicInventory.documents) {
          this.downloadServerPDF(documents);
        }
      } else {
        this.toasterService.info(MESSAGES.noDocumentsAvailable);
      }
    })
  }
}
