<!-- Image Zoom Overlay -->
<div class="image-zoom-overlay" *ngIf="isVisible">
  <!-- Close Button -->
  <button class="zoom-close-btn" (click)="closeOverlay()">
    <fa-icon [icon]="faIcons.faTimes"></fa-icon>
  </button>

  <!-- Image Counter -->
  <div class="image-counter">
    {{ selectedIndex + 1 }} / {{ images.length }}
  </div>

  <!-- Navigation Arrows -->
  <button class="nav-arrow nav-arrow-left" 
          (click)="navigateImage('prev')"
          [disabled]="selectedIndex === 0">
    <fa-icon [icon]="faIcons.faArrowLeft"></fa-icon>
  </button>
  
  <button class="nav-arrow nav-arrow-right" 
          (click)="navigateImage('next')"
          [disabled]="selectedIndex === images.length - 1">
    <fa-icon [icon]="faIcons.faArrowDown"></fa-icon>
  </button>

  <!-- Zoom Controls -->
  <div class="zoom-controls">
    <button class="zoom-btn" (click)="zoomIn()" [disabled]="zoomLevel >= maxZoom">
      <fa-icon [icon]="faIcons.faAdd"></fa-icon>
    </button>
    <span class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</span>
    <button class="zoom-btn" (click)="zoomOut()" [disabled]="zoomLevel <= minZoom">
      <fa-icon [icon]="faIcons.faMinus"></fa-icon>
    </button>
    <button class="zoom-btn" (click)="resetZoom()">
      <fa-icon [icon]="faIcons.faRefresh"></fa-icon>
    </button>
    <button class="zoom-btn" (click)="toggleMagnifier()" [class.active]="magnifierEnabled">
      <fa-icon [icon]="faIcons.faMagnifyingGlass"></fa-icon>
    </button>
  </div>

  <!-- Main Image Container -->
  <div class="zoom-image-container" 
       (wheel)="onWheel($event)"
       (mousedown)="startPan($event)"
       (mousemove)="onPan($event)"
       (mouseup)="endPan()"
       (mouseleave)="endPan()">
    
    <img #zoomImage
         class="zoom-image"
         [src]="getCurrentImage()?.fullUrl"
         [style.transform]="getImageTransform()"
         [style.transition]="isPanning ? 'none' : 'transform 0.3s ease'"
         (load)="onImageLoad()"
         (dragstart)="$event.preventDefault()"
         [alt]="getCurrentImage()?.alt || 'Zoomed Image'">

    <!-- Magnifier Glass -->
    <div class="magnifier-glass" 
         [ngStyle]="getMagnifierStyle()">
    </div>
  </div>
</div>
