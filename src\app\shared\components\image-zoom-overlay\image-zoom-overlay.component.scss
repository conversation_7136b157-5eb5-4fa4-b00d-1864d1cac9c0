/* Image Zoom Overlay Styles */
.image-zoom-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.95);
  z-index: 10000;
  display: flex;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(5px);
}

/* Close Button */
.zoom-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 24px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
  z-index: 10001;
}

.zoom-close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Image Counter */
.image-counter {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  background: rgba(0, 0, 0, 0.7);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  z-index: 10001;
}

/* Navigation Arrows */
.nav-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 24px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
  z-index: 10001;
}

.nav-arrow:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
}

.nav-arrow:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.nav-arrow-left {
  left: 20px;
}

.nav-arrow-right {
  right: 20px;
}

.nav-arrow-right fa-icon {
  transform: rotate(-90deg); /* Rotate arrow down to point right */
}

/* Zoom Controls */
.zoom-controls {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(0, 0, 0, 0.7);
  padding: 10px 20px;
  border-radius: 25px;
  z-index: 10001;
}

.zoom-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 16px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}

.zoom-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
}

.zoom-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.zoom-btn.active {
  background: rgba(255, 255, 255, 0.4);
  color: #007bff;
}

.zoom-level {
  color: white;
  font-size: 14px;
  min-width: 50px;
  text-align: center;
}

/* Image Container */
.zoom-image-container {
  position: relative;
  width: 90vw;
  height: 90vh;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.zoom-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transform-origin: center center;
  user-select: none;
  -webkit-user-drag: none;
}

/* Magnifier Glass */
.magnifier-glass {
  position: absolute;
  width: 150px;
  height: 150px;
  border: 3px solid #fff;
  border-radius: 50%;
  background-repeat: no-repeat;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
  pointer-events: none;
  z-index: 10002;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .zoom-close-btn {
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .image-counter {
    top: 10px;
    font-size: 12px;
    padding: 6px 12px;
  }

  .nav-arrow {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .nav-arrow-left {
    left: 10px;
  }

  .nav-arrow-right {
    right: 10px;
  }
  
  .nav-arrow-right fa-icon {
    transform: rotate(-90deg); /* Ensure rotation works on mobile too */
  }

  .zoom-controls {
    bottom: 20px;
    padding: 8px 16px;
    gap: 8px;
  }

  .zoom-btn {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }

  .zoom-level {
    font-size: 12px;
    min-width: 40px;
  }

  .zoom-image-container {
    width: 95vw;
    height: 85vh;
  }

  .magnifier-glass {
    width: 120px;
    height: 120px;
    border-width: 2px;
  }
}
