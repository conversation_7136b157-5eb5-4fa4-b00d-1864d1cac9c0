import { Component, Input, Output, EventEmitter, ViewChild, ElementRef, HostListener, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faIcons } from '../../../@core/utils/font-awesome-icon.utils';

export interface ZoomImage {
  fullUrl: string;
  alt?: string;
}

@Component({
  selector: 'app-image-zoom-overlay',
  standalone: true,
  imports: [CommonModule, FontAwesomeModule],
  templateUrl: './image-zoom-overlay.component.html',
  styleUrls: ['./image-zoom-overlay.component.scss']
})
export class ImageZoomOverlayComponent implements OnInit, OnDestroy {
  @Input() images: ZoomImage[] = [];
  @Input() selectedIndex: number = 0;
  @Input() isVisible: boolean = false;
  @Output() close = new EventEmitter<void>();
  @Output() indexChange = new EventEmitter<number>();

  @ViewChild('zoomImage', { static: false }) zoomImageRef!: ElementRef<HTMLImageElement>;

  // Zoom state
  zoomLevel: number = 1;
  minZoom: number = 0.5;
  maxZoom: number = 5;
  panX: number = 0;
  panY: number = 0;

  // Pan state
  isPanning: boolean = false;
  lastPanX: number = 0;
  lastPanY: number = 0;

  // Magnifier state
  magnifierEnabled: boolean = false;
  showMagnifier: boolean = false;
  magnifierX: number = 0;
  magnifierY: number = 0;
  magnifierBackgroundX: number = 0;
  magnifierBackgroundY: number = 0;

  faIcons = faIcons;
  Math = Math;

  ngOnInit(): void {
    if (this.isVisible) {
      document.body.style.overflow = 'hidden';
    }
  }

  ngOnDestroy(): void {
    document.body.style.overflow = 'auto';
  }

  @HostListener('document:keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    if (!this.isVisible) return;

    switch (event.key) {
      case 'Escape':
        this.closeOverlay();
        break;
      case 'ArrowLeft':
        this.navigateImage('prev');
        break;
      case 'ArrowRight':
        this.navigateImage('next');
        break;
      case '+':
      case '=':
        this.zoomIn();
        break;
      case '-':
        this.zoomOut();
        break;
      case '0':
        this.resetZoom();
        break;
    }
  }

  closeOverlay(): void {
    this.isVisible = false;
    this.resetZoomState();
    document.body.style.overflow = 'auto';
    this.close.emit();
  }

  navigateImage(direction: 'prev' | 'next'): void {
    if (direction === 'prev' && this.selectedIndex > 0) {
      this.selectedIndex--;
    } else if (direction === 'next' && this.selectedIndex < this.images.length - 1) {
      this.selectedIndex++;
    }
    this.indexChange.emit(this.selectedIndex);
    this.resetZoomState();
  }

  getCurrentImage(): ZoomImage | undefined {
    return this.images[this.selectedIndex];
  }

  // Zoom Control Methods
  zoomIn(): void {
    if (this.zoomLevel < this.maxZoom) {
      this.zoomLevel = Math.min(this.zoomLevel * 1.2, this.maxZoom);
    }
  }

  zoomOut(): void {
    if (this.zoomLevel > this.minZoom) {
      this.zoomLevel = Math.max(this.zoomLevel / 1.2, this.minZoom);
      if (this.zoomLevel <= 1) {
        this.panX = 0;
        this.panY = 0;
      }
    }
  }

  resetZoom(): void {
    this.zoomLevel = 1;
    this.panX = 0;
    this.panY = 0;
  }

  onWheel(event: WheelEvent): void {
    event.preventDefault();
    if (event.deltaY < 0) {
      this.zoomIn();
    } else {
      this.zoomOut();
    }
  }

  // Pan Methods
  startPan(event: MouseEvent): void {
    if (this.magnifierEnabled) {
      this.showMagnifier = true;
      this.updateMagnifierPosition(event);
      return;
    }

    this.isPanning = true;
    this.lastPanX = event.clientX;
    this.lastPanY = event.clientY;
  }

  onPan(event: MouseEvent): void {
    if (this.magnifierEnabled && this.showMagnifier) {
      this.updateMagnifierPosition(event);
      return;
    }

    if (!this.isPanning) return;

    const deltaX = event.clientX - this.lastPanX;
    const deltaY = event.clientY - this.lastPanY;

    this.panX += deltaX;
    this.panY += deltaY;

    this.lastPanX = event.clientX;
    this.lastPanY = event.clientY;
  }

  endPan(): void {
    this.isPanning = false;
    this.showMagnifier = false;
  }

  // Magnifier Methods
  toggleMagnifier(): void {
    this.magnifierEnabled = !this.magnifierEnabled;
    this.showMagnifier = false;
  }

  updateMagnifierPosition(event: MouseEvent): void {
    if (!this.zoomImageRef?.nativeElement) return;

    const img = this.zoomImageRef.nativeElement;
    const rect = img.getBoundingClientRect();
    
    // Position magnifier glass
    this.magnifierX = event.clientX - 75; // Center the 150px magnifier
    this.magnifierY = event.clientY - 75;
    
    // Calculate background position for magnified view
    const relativeX = event.clientX - rect.left;
    const relativeY = event.clientY - rect.top;
    
    // Scale the background position
    const scaleX = (relativeX / rect.width) * 100;
    const scaleY = (relativeY / rect.height) * 100;
    
    this.magnifierBackgroundX = scaleX;
    this.magnifierBackgroundY = scaleY;
  }

  // Helper Methods
  getImageTransform(): string {
    return `scale(${this.zoomLevel}) translate(${this.panX / this.zoomLevel}px, ${this.panY / this.zoomLevel}px)`;
  }

  getMagnifierStyle(): any {
    if (!this.showMagnifier || !this.getCurrentImage()) return { display: 'none' };
    
    return {
      display: 'block',
      left: this.magnifierX + 'px',
      top: this.magnifierY + 'px',
      backgroundImage: `url(${this.getCurrentImage()?.fullUrl})`,
      backgroundPosition: `${this.magnifierBackgroundX}% ${this.magnifierBackgroundY}%`,
      backgroundSize: '300%' // 3x magnification
    };
  }

  onImageLoad(): void {
    // Reset zoom state when new image loads
    this.resetZoomState();
  }

  private resetZoomState(): void {
    this.zoomLevel = 1;
    this.panX = 0;
    this.panY = 0;
    this.isPanning = false;
    this.showMagnifier = false;
    this.magnifierEnabled = false;
  }
}
